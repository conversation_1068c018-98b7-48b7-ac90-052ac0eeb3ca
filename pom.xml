<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.safaricom.dxl</groupId>
		<artifactId>dxl-webflux-starter-parent</artifactId>
		<version>1.4.5</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.safaricom.dxl</groupId>
	<artifactId>ms-partner-proxy</artifactId>
	<version>1.0.0</version>
	<name>ms-partner-proxy</name>
	<description>DXL Framework Spring Boot Application</description>
	<properties>
		<java.version>17</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<sonar.exclusions>
			**/MsPartnerProxyApplication.java
		</sonar.exclusions>
		<sonar.coverage.exclusions>
			**/config/*.java,
			**/controller/*.java,
			**/exception/*.java,
			**/model/*.java,
			**/repository/*.java,
			**/utils/*.java,
			**/MsPartnerProxyApplication.java
		</sonar.coverage.exclusions>
	</properties>
	<dependencies>
	</dependencies>
	<repositories>
		<repository>
			<id>dxl-releases</id>
			<name>dxl-releases</name>
			<url>https://jfrog.safaricom.co.ke/artifactory/dxl-releases/</url>
		</repository>
		<repository>
			<id>dxl-snapshots</id>
			<name>dxl-snapshot</name>
			<url>https://jfrog.safaricom.co.ke/artifactory/dxl-snapshot/</url>
		</repository>
	</repositories>

</project>
