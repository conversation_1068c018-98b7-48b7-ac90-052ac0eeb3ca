FROM 559104660845.dkr.ecr.eu-west-1.amazonaws.com/amazoncorretto:17-alpine3.20-jdk

LABEL maintainer="<EMAIL>"

EXPOSE 8080

VOLUME /tmp

ADD target/ms-partner-proxy-1.0.0.jar ms-partner-proxy.jar

RUN /bin/sh -c 'touch /ms-partner-proxy.jar'

ENV TZ=Africa/Nairobi

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

ENTRYPOINT ["java","-Xmx256m", "-XX:+UseG1GC", "-Djava.security.egd=file:/dev/./urandom","-jar","/ms-partner-proxy.jar"]
