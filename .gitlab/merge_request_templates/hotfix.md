### Issue
Provide a link to the original issue or bug report.

### Problem
Describe the critical issue or bug being addressed.

### Solution
Explain the fix or solution implemented.

### Changes Made
Provide code snippets or screenshots as needed.

### Additional Notes
Include any extra information or considerations for reviewers, such as impacted areas of the codebase.

### Merge Request Checklists
- [ ] Code follows project coding guidelines.
- [ ] Documentation reflects the changes made.
- [ ] No new issues introduced.
- [ ] Tested and approved by QA.