package com.safaricom.dxl.partner.proxy.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * Context object that holds the state of a request as it passes through the processing pipeline.
 * This allows processors to share information and modify the request context.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class RequestContext {
    @Getter
    private final ServerWebExchange exchange;

    @Getter
    private final Mono<byte[]> body;

    @Getter @Setter
    private Resource resource;

    @Getter @Setter
    private UserProfile userProfile;

    private final Map<String, Object> attributes = new HashMap<>();

    /**
     * Gets the HTTP method of the request.
     *
     * @return The HTTP method
     */
    public HttpMethod getMethod() {
        return exchange.getRequest().getMethod();
    }

    /**
     * Gets the path of the request.
     *
     * @return The request path
     */
    public String getPath() {
        return exchange.getRequest().getPath().pathWithinApplication().value();
    }

    /**
     * Gets the query string of the request.
     *
     * @return The query string
     */
    public String getQuery() {
        return exchange.getRequest().getURI().getRawQuery();
    }

    /**
     * Gets the headers of the request.
     *
     * @return The request headers
     */
    public HttpHeaders getHeaders() {
        return exchange.getRequest().getHeaders();
    }

    /**
     * Sets an attribute in the context.
     *
     * @param key The attribute key
     * @param value The attribute value
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    /**
     * Gets an attribute from the context.
     *
     * @param key The attribute key
     * @param <T> The attribute type
     * @return The attribute value
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    /**
     * Checks if an attribute exists in the context.
     *
     * @param key The attribute key
     * @return true if the attribute exists, false otherwise
     */
    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }

    /**
     * Removes an attribute from the context.
     *
     * @param key The attribute key
     */
    public void removeAttribute(String key) {
        attributes.remove(key);
    }
}
