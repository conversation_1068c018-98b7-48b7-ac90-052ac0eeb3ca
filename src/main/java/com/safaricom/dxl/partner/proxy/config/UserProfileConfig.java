package com.safaricom.dxl.partner.proxy.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "dxl.ms.user-profile")
public class UserProfileConfig {
    private String cachePrefix;
    private String cacheDuration;
}
