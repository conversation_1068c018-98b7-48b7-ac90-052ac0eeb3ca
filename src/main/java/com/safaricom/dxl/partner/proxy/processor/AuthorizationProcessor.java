package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.UserProfile;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.ES;

/**
 * Processor that authorizes the user based on their permissions.
 *
 * <AUTHOR>
 */
@Component
public class AuthorizationProcessor extends BaseIdentityProcessor {

    public AuthorizationProcessor(ResponseUtils responseUtils) {
        super(responseUtils);
    }

    @Override
    protected int getDefaultOrder() {
        return 60; // Execute after authentication
    }

    // init() method inherited from BaseIdentityProcessor

    @Override
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        Resource resource = context.getResource();
        // Defensive: if resource is null, do not process
        if (resource == null) {
            return false;
        }
        // Only process if the resource requires a permission and is not the user profile endpoint
        return StringUtils.isNotBlank(resource.getPermission()) &&
               !shouldProcessBasedOnOperation(context, GET_LOGGED_IN_USER_PROFILE, true);
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        Resource resource = context.getResource();
        UserProfile userProfile = context.getUserProfile();

        LoggingUtils.debug("Checking permission '{}' for user '{}'",
                resource.getPermission(), userProfile.getUsername());

        if (!userProfile.getPermissions().contains(resource.getPermission())) {
            LoggingUtils.warn("User '{}' lacks required permission: {}", userProfile.getUsername(), resource.getPermission());

            return createErrorResponse(context.getHeaders(), resource, HttpStatus.UNAUTHORIZED, "GW_ERR7", ES)
                    .map(ProcessingResult::terminate);
        }

        WsLogManager.logger.debug("User '{}' has required permission: {}", userProfile.getUsername(), resource.getPermission());

        return Mono.just(ProcessingResult.continueProcessing());
    }
}
