package com.safaricom.dxl.partner.proxy.model;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class Resource implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @NotBlank
    private String method;

    @NotBlank
    private String path;

    @NotBlank
    private String endpoint;
    @NotBlank
    private String operation;
    private String microservice;
    private String authType = "basic";
    private String basicAuthCredentials = "development";
    private Boolean exemptMsisdnHeader = false;
    private Boolean logActivity = false;
    private String permission;
    private String fullPath;
}
