# Request Processing Pipeline

This package contains the components of the request processing pipeline, which is a flexible and extensible architecture for processing HTTP requests in the MS Partner Proxy service.

## Architecture

The request processing pipeline is based on the Chain of Responsibility and Strategy design patterns. It consists of the following components:

1. **RequestContext**: Holds the state of a request as it passes through the pipeline.
2. **ProcessingResult**: Represents the result of a processing step, which can either continue processing or terminate with a response.
3. **RequestProcessor**: Interface for processors in the pipeline.
4. **AbstractRequestProcessor**: Base class for processors that provides common functionality.
5. **RequestPipeline**: Orchestrates the execution of processors in order.

## Flow

1. A request is received by the `ProxyController`.
2. The controller delegates to the `ProxyService`.
3. The service creates a `RequestContext` and passes it to the `RequestPipeline`.
4. The pipeline executes each processor in order.
5. Each processor can either:
   - Continue processing, allowing the next processor to execute.
   - Terminate processing with a response, which is returned to the client.


## Processors


The following processors are included (in typical order):

1. **ResourceResolutionProcessor**: Resolves the appropriate resource configuration for the request.
2. **LoggingProcessor**: Logs request details and adds a conversation ID if missing.
3. **HeaderValidationProcessor**: Validates required headers based on resource configuration.
4. **UserProfileProcessor**: Handles requests for user profile information.
5. **UserLoginProcessor**: Caches user profile data (email and phone) on user login operations for fast access.
6. **UserLogoutProcessor**: Deletes user profile data from the cache on user logout operations.
7. **AuthenticationProcessor**: Authenticates the user by retrieving their profile.
8. **AuthorizationProcessor**: Authorizes the user based on their permissions.
9. **UserProfileCachingProcessor**: Caches user profile data (email and phone) as a JSON object for faster access during other operations.
10. **TimeoutProcessor**: Adds configurable timeout handling to requests.
11. **RequestForwardingProcessor**: Forwards the request to the target service.

**UserLoginProcessor** and **UserLogoutProcessor** now run before authentication and authorization, and **UserProfileCachingProcessor** runs after authorization. This ensures user profile cache is managed correctly during login and logout flows, and authentication/authorization are performed at the right stage.

**UserLoginProcessor**: On login operations (e.g., `POST_USER_LOGIN`), this processor caches the user's profile data (such as email and phone) for subsequent requests. It uses robust null handling and only processes relevant operations.

**UserLogoutProcessor**: On logout operations (e.g., `POST_USER_LOGOUT`), this processor deletes the user's profile data from the cache, ensuring that sensitive information is not retained after logout.

Note: The **UserParamCachingProcessor** has been replaced by the **UserProfileCachingProcessor**, which provides more robust caching of user profile attributes.

## Configuration

Processors can be configured through various configuration properties:

### Enabling/Disabling Processors

```properties
# Enable/disable specific processors
dxl.ms.processors.enabled.ResourceResolutionProcessor=true
dxl.ms.processors.enabled.HeaderValidationProcessor=true
# ...
```

### Configuring Processor Order

```properties
# Configure processor order (lower values execute earlier)
dxl.ms.processors.order.ResourceResolutionProcessor=1
dxl.ms.processors.order.LoggingProcessor=10
dxl.ms.processors.order.HeaderValidationProcessor=20
dxl.ms.processors.order.UserProfileProcessor=30
dxl.ms.processors.order.UserLoginProcessor=40
dxl.ms.processors.order.UserLogoutProcessor=41
dxl.ms.processors.order.AuthenticationProcessor=50
dxl.ms.processors.order.AuthorizationProcessor=60
dxl.ms.processors.order.UserProfileCachingProcessor=70
dxl.ms.processors.order.TimeoutProcessor=90
dxl.ms.processors.order.RequestForwardingProcessor=100
# ...
```

### Processor-Specific Settings

```properties
# Processor-specific settings
dxl.ms.processors.settings.HeaderValidationProcessor.strictValidation=true
dxl.ms.processors.settings.TimeoutProcessor.timeoutSeconds=30
dxl.ms.processors.settings.RequestForwardingProcessor.enableKafkaStreaming=false
# ...
```

### Processor-Specific Configurations

```properties
# Processor-specific configurations (for more complex settings)

# UserProfileCachingProcessor configuration
dxl.ms.user-profile.cache-duration=PT55M
# ...
```

## Creating Custom Processors

To create a custom processor:

1. Create a class that extends `AbstractRequestProcessor`.
2. Annotate it with `@Component` and `@RequiredArgsConstructor` for dependency injection.
3. Override the `getDefaultOrder` method to specify its position in the pipeline.
4. Add a `@PostConstruct` init method for initialization logging.
5. Override the `process` method to implement the processing logic.
6. Optionally override `shouldProcessIfEnabled` to implement conditional processing.

Example:

```java
@Component
@RequiredArgsConstructor
public class CustomProcessor extends AbstractRequestProcessor {

    private final SomeDependency dependency;

    @Override
    protected int getDefaultOrder() {
        return 15; // Specify the default order
    }

    @PostConstruct
    public void init() {
        LoggingUtils.info("CustomProcessor initialized");
    }

    @Override
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        // Determine whether this processor should process the request
        return true;
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        // Get processor settings from configuration
        String setting = getSetting("mySetting", "defaultValue");

        // Get processor-specific configurations
        Map<String, String> configs = getConfigs("myConfigs");

        // Process the request
        // ...

        // Continue processing
        return Mono.just(ProcessingResult.continueProcessing());

        // Or terminate with a response
        // return responseUtils.someResponse().map(ProcessingResult::terminate);
    }
}
```

## Processor Details

### UserProfileCachingProcessor

This processor caches user profile data (email and phone) as a JSON object for faster access in subsequent requests. It:

1. Runs after the AuthorizationProcessor with an order of 41.
2. Extracts the email and phone number from the user profile in the request context.
3. Creates a CachedUserProfile object containing these attributes.
4. Skips caching if both email and phone are null or empty.
5. Uses the x-token header value to generate a unique cache key.
6. Checks if the user profile data is already cached before caching.
7. Caches the data with an expiry configured in UserProfileConfig (default: 55 minutes).
8. Implements defensive programming to handle null values and configuration issues.
9. Always continues processing regardless of caching success or failure.

## Dependency Injection

The processors use Spring's dependency injection to receive their dependencies:

1. **ResourceRegistry**: Holds validated resource configurations loaded at startup.
2. **ResponseUtils**: Provides utility methods for creating responses.
3. **CacheRepository**: Provides access to the cache for storing and retrieving data.
4. **Other Services**: Any other services needed by the processors.

## Reactive Programming

All processors in the pipeline are fully reactive, using Project Reactor:

1. **Mono/Flux**: All methods return `Mono` or `Flux` types.
2. **Non-Blocking**: Operations are non-blocking to maximize throughput.
3. **Schedulers**: CPU-intensive operations use dedicated thread pools.
4. **Error Handling**: Errors are handled using reactive operators like `onErrorResume`.

## Defensive Programming

Processors implement defensive programming techniques to ensure robustness:

1. **Null Checks**: All inputs are checked for null values before use.
2. **Empty Checks**: String and collection inputs are checked for emptiness.
3. **Default Values**: Sensible defaults are provided when configurations are missing.
4. **Error Handling**: Exceptions are caught and logged, with processing continuing when possible.
5. **Validation**: Input validation is performed before processing.
6. **Fallbacks**: Alternative paths are provided when primary operations fail.
7. **Logging**: Detailed logging is used to track processor behavior and diagnose issues.

## Benefits

This architecture provides several benefits:

1. **Extensibility**: New processing steps can be added without modifying existing code.
2. **Maintainability**: Each processor has a single responsibility.
3. **Testability**: Processors can be tested in isolation.
4. **Configurability**: Processing pipeline can be configured for different environments.
5. **Readability**: Clear separation of concerns makes code easier to understand.
6. **Flexibility**: Conditional processing allows for complex routing logic.
7. **Performance**: Fully reactive approach maximizes throughput and resource utilization.
8. **Resilience**: Error handling at each step prevents cascading failures.
