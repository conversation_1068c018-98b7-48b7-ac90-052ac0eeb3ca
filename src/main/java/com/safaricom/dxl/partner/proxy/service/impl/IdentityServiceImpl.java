package com.safaricom.dxl.partner.proxy.service.impl;


import com.safaricom.dxl.partner.proxy.config.BasicAuthConfig;
import com.safaricom.dxl.partner.proxy.config.UserProfileConfig;
import com.safaricom.dxl.partner.proxy.model.UserInfoResponse;
import com.safaricom.dxl.partner.proxy.model.UserProfile;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.service.IdentityService;
import com.safaricom.dxl.partner.proxy.service.ResourceRegistry;
import com.safaricom.dxl.partner.proxy.utils.MsExtensions;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.extractUsernameFromEmail;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_SOURCE_SYSTEM;

/**
 * <AUTHOR>
 */

@Service
@RequiredArgsConstructor
@ExtensionMethod(MsExtensions.class)
public class IdentityServiceImpl implements IdentityService {

    private final WebClient webClient;
    private final CacheRepository cacheRepository;
    private final ResourceRegistry resourceRegistry;
    private final BasicAuthConfig basicAuthConfig;
    private final UserProfileConfig userProfileConfig;

    public Mono<UserInfoResponse> getUserInfo(HttpHeaders headers) {
        return resourceRegistry.getResources().stream()
                .filter(resource -> GET_LOGGED_IN_USER_PROFILE.equals(resource.getOperation()))
                .findFirst()
                .map(resource -> webClient
                        .get()
                        .uri(resource.getEndpoint())
                        .headers(httpHeaders -> {
                            httpHeaders.addAll(headers);
                            httpHeaders.removeIdentityHeaders();
                            httpHeaders.set(X_SOURCE_SYSTEM, "identity");
                            if (resource.getAuthType().equals("basic")) {
                                httpHeaders.setBasicAuth(basicAuthConfig.getCredentials().get(resource.getBasicAuthCredentials()));
                            }
                        })
                        .retrieve()
                        .bodyToMono(UserInfoResponse.class)
                        .filter(response -> 200 == response.getHeader().getResponseCode())
                        .switchIfEmpty(Mono.error(new Throwable(GET_LOGGED_IN_USER_PROFILE.concat(" returned failure response"))))
                        .flatMap(response -> {
                            response.getBody().setUsername(extractUsernameFromEmail(response.getBody().getEmail()));
                            return cacheRepository.setData(userProfileConfig.getCachePrefix() + headers.getFirst(X_IDENTITY), response.getBody(), Duration.parse(userProfileConfig.getCacheDuration()))
                                    .then(Mono.just(response));
                        }))
                .orElse(Mono.error(new Throwable(GET_LOGGED_IN_USER_PROFILE.concat(" resource not configured"))));
    }

    public Mono<UserProfile> getUserProfile(HttpHeaders headers) {
        return cacheRepository.getData(userProfileConfig.getCachePrefix() + headers.getFirst(X_IDENTITY), UserProfile.class)
                .switchIfEmpty(getUserInfo(headers)
                        .filter(response -> 200 == response.getHeader().getResponseCode())
                        .flatMap(response -> Mono.just(response.getBody())));
    }
}
