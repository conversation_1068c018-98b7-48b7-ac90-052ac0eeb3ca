package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.BasicAuthConfig;
import com.safaricom.dxl.partner.proxy.model.*;
import com.safaricom.dxl.partner.proxy.service.StreamService;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.MsExtensions;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import jakarta.annotation.PostConstruct;
import lombok.experimental.ExtensionMethod;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.netty.channel.AbortedException;

import java.io.IOException;
import java.net.ConnectException;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeoutException;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

/**
 * Processor that forwards the request to the target service.
 * This is typically the last processor in the chain.
 *
 * <AUTHOR>
 */
@Component
@ExtensionMethod(MsExtensions.class)
public class RequestForwardingProcessor extends AbstractRequestProcessor {

    private final WebClient webClient;
    private final BasicAuthConfig basicAuthConfig;
    private final StreamService streamService;
    private final ResponseUtils responseUtils;

    public RequestForwardingProcessor(WebClient webClient, BasicAuthConfig basicAuthConfig, StreamService streamService, ResponseUtils responseUtils) {
        this.webClient = webClient;
        this.basicAuthConfig = basicAuthConfig;
        this.streamService = streamService;
        this.responseUtils = responseUtils;
    }

    @Override
    protected int getDefaultOrder() {
        return 100; // Execute last in the chain
    }

    /**
     * Logs initialization to confirm dependency injection is working.
     */
    @PostConstruct
    public void init() {
        LoggingUtils.info("RequestForwardingProcessor initialized");
    }

    /**
     * Determines whether the current operation should skip user profile-dependent logic.
     * This prevents NullPointerExceptions when userProfile is null for excluded operations.
     *
     * @param context The request context
     * @return true if user profile logic should be skipped, false otherwise
     */
    private boolean shouldSkipUserProfileLogic(RequestContext context) {
        // Skip user profile logic for operations where authentication was skipped
        boolean isUserProfileOperation = shouldProcessBasedOnOperation(context, GET_LOGGED_IN_USER_PROFILE, true);
        boolean isUserRegistrationOperation = shouldProcessBasedOnOperation(context, POST_PARTNER_HUB_USER_REGISTRATION, true);
        return isUserProfileOperation || isUserRegistrationOperation;
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpMethod method = context.getMethod();
        String query = context.getQuery();
        HttpHeaders headers = context.getHeaders();
        Resource resource = context.getResource();
        UserProfile userProfile = context.getUserProfile();
        Mono<byte[]> body = context.getBody();

        LoggingUtils.debug("Forwarding request to target service: {} {}", method, resource.getEndpoint());

        // Resolve endpoint with path variables
        Map<String, String> pathVariables = context.getAttribute("pathVariables");
        String resolvedEndpoint = resolveEndpoint(resource.getEndpoint(), pathVariables);
        URI targetUri = URI.create(resolvedEndpoint + (Objects.nonNull(query) ? "?" + query : StringUtils.EMPTY));

        return forwardRequest(method, targetUri, headers, body, userProfile, resource, context)
                .map(ProcessingResult::terminate)
                .flatMap(result -> logActivity(headers, userProfile, resource, context).thenReturn(result))
                .onErrorResume(throwable -> {
                    LoggingUtils.error("Error forwarding request: {}", throwable.getMessage());
                    return handleWebClientError(throwable, headers, resource)
                            .map(ProcessingResult::terminate);
                });
    }

    /**
     * Forwards the request to the target service.
     *
     * @param method The HTTP method
     * @param targetUri The target URI
     * @param headers The request headers
     * @param body The request body
     * @param userProfile The user profile (may be null for excluded operations)
     * @param resource The resource configuration
     * @param context The request context
     * @return A Mono that emits the response entity
     */
    private Mono<ResponseEntity<byte[]>> forwardRequest(HttpMethod method, URI targetUri, HttpHeaders headers,
                                                        Mono<byte[]> body, UserProfile userProfile, Resource resource, RequestContext context) {
        boolean skipUserProfileLogic = shouldSkipUserProfileLogic(context);

        return webClient
                .method(method)
                .uri(targetUri)
                .headers(httpHeaders -> {
                    httpHeaders.addAll(headers);
                    httpHeaders.removeIdentityHeaders();

                    // Only set user profile-related headers if userProfile is available
                    if (!skipUserProfileLogic && userProfile != null) {
                        httpHeaders.set(X_IDENTITY, userProfile.getUsername());
                        httpHeaders.set(X_IDENTITY_SUB, userProfile.getSub());
                    }

                    httpHeaders.set(X_SOURCE_SYSTEM, "partner-portal");
                    httpHeaders.set(X_APP, "partner-portal");

                    // Handle X_TOKEN header defensively
                    String tokenHeader = headers.getFirst(X_TOKEN);
                    if (tokenHeader != null && tokenHeader.length() > 7) {
                        httpHeaders.set(X_TOKEN, tokenHeader.substring(7)); // Remove Bearer prefix if it exists
                    } else if (tokenHeader != null) {
                        httpHeaders.set(X_TOKEN, tokenHeader); // Use as-is if shorter than expected
                    }

                    if ("basic".equals(resource.getAuthType())) {
                        httpHeaders.setBasicAuth(basicAuthConfig.getCredentials().get(resource.getBasicAuthCredentials()));
                    }
                })
                .body(body != null ? body : Mono.empty(), byte[].class)
                .retrieve()
                .onStatus(HttpStatusCode::isError, response -> {
                    LoggingUtils.info("{} Error Encountered on Target Service: {}", response.statusCode(), resource.getMicroservice());
                    return Mono.empty();
                })
                .toEntity(byte[].class)
                .map(responseEntity -> {
                    HttpHeaders responseHeaders = new HttpHeaders();
                    responseHeaders.addAll(responseEntity.getHeaders());
                    responseHeaders.remove(X_MESSAGE_ID);
                    responseHeaders.setSecurityHeaders();
                    return ResponseEntity.status(responseEntity.getStatusCode()).headers(responseHeaders).body(responseEntity.getBody());
                });
    }

    /**
     * Logs activity for auditing purposes.
     *
     * @param headers The request headers
     * @param userProfile The user profile (may be null for excluded operations)
     * @param resource The resource configuration
     * @param context The request context
     * @return A Mono that completes when the activity has been logged
     */
    private Mono<Void> logActivity(HttpHeaders headers, UserProfile userProfile, Resource resource, RequestContext context) {
        // Check if activity logging is enabled in the configuration
        String enableKafkaStr = getSetting("enableKafkaStreaming", "false");
        boolean enableKafkaStream = Boolean.parseBoolean(enableKafkaStr);

        if (!enableKafkaStream) {
            LoggingUtils.debug("Activity logging is disabled. Set 'enableKafkaStreaming' to true to enable.");
            return Mono.empty();
        }

        if (Boolean.FALSE.equals(resource.getLogActivity())) {
            LoggingUtils.debug("Skipping activity logging for the operation: {}", resource.getOperation());
            return Mono.empty();
        }

        // Skip activity logging if userProfile is null (for excluded operations)
        boolean skipUserProfileLogic = shouldSkipUserProfileLogic(context);
        if (skipUserProfileLogic || userProfile == null) {
            LoggingUtils.debug("Skipping activity logging for operation {} - userProfile not available", resource.getOperation());
            return Mono.empty();
        }

        // Log the activity to Kafka
        LoggingUtils.info("Logging activity to Kafka for operation: {} | Identity: {}", resource.getOperation(),
                userProfile.getUsername());
        return streamService.publish(new Activity(
                headers.getFirst(X_CONVERSATION_ID),
                userProfile.getSub(),
                userProfile.getEmail(),
                userProfile.getPhoneNumber(),
                resource.getOperation(),
                LocalDateTime.now()
        ));
    }

    /**
     * Resolves an endpoint template with path variables.
     *
     * @param endpointTemplate The endpoint template
     * @param pathVariables The path variables
     * @return The resolved endpoint
     */
    private String resolveEndpoint(String endpointTemplate, Map<String, String> pathVariables) {
        if (pathVariables == null) {
            return endpointTemplate;
        }

        String resolved = endpointTemplate;
        for (Map.Entry<String, String> entry : pathVariables.entrySet()) {
            resolved = resolved.replace("{" + entry.getKey() + "}", entry.getValue());
        }

        return resolved;
    }

    /**
     * Handles errors that occur during request forwarding.
     *
     * @param throwable The error that occurred
     * @param headers The request headers
     * @param resource The resource being processed
     * @return A Mono that emits a response entity with an appropriate error message
     */
    private Mono<ResponseEntity<byte[]>> handleWebClientError(Throwable throwable, HttpHeaders headers, Resource resource) {
        if (throwable instanceof WebClientResponseException response &&
                !response.getStatusCode().equals(HttpStatus.SERVICE_UNAVAILABLE)) {
            response.getHeaders().setSecurityHeaders();
            return Mono.just(ResponseEntity.status(response.getStatusCode())
                    .headers(response.getHeaders())
                    .body(response.getResponseBodyAsByteArray()));
        }

        if (throwable instanceof WebClientResponseException) {
            return responseUtils.errorResponseReactive(headers, resource, HttpStatus.SERVICE_UNAVAILABLE,
                    "GW_ERR2", throwable.getMessage());
        } else if (throwable instanceof WebClientRequestException) {
            return responseUtils.errorResponseReactive(headers, resource, HttpStatus.SERVICE_UNAVAILABLE,
                    "GW_ERR2", throwable.getMessage());
        } else if (throwable instanceof ConnectException || throwable instanceof AbortedException) {
            return responseUtils.errorResponseReactive(headers, resource, HttpStatus.SERVICE_UNAVAILABLE,
                    "GW_ERR3", throwable.getMessage());
        } else if (throwable instanceof TimeoutException) {
            return responseUtils.errorResponseReactive(headers, resource, HttpStatus.GATEWAY_TIMEOUT,
                    "GW_ERR4", throwable.getMessage());
        } else if (throwable instanceof IOException) {
            return responseUtils.errorResponseReactive(headers, resource, HttpStatus.BAD_GATEWAY,
                    "GW_ERR5", throwable.getMessage());
        } else {
            return responseUtils.errorResponseReactive(headers, resource, HttpStatus.BAD_GATEWAY,
                    "GW_ERR6", throwable.getMessage());
        }
    }
}
