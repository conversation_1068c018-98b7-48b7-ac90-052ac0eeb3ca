package com.safaricom.dxl.partner.proxy.service;


import com.safaricom.dxl.partner.proxy.model.Activity;
import reactor.core.publisher.Mono;

/**
 * Service interface for streaming activities to Kafka.
 *
 * <AUTHOR>
 */
public interface StreamService {
    /**
     * Publishes an activity to the configured Kafka topic.
     *
     * @param activity The activity to publish
     * @return A Mono that completes when the activity has been published
     */
    Mono<Void> publish(Activity activity);
}
