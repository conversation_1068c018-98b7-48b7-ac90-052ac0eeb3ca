package com.safaricom.dxl.partner.proxy.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

@Data
public class ResourceConfig implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @NotBlank
    private String group;

    @NotEmpty
    private List<Resource> resources;
}