package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import jakarta.annotation.PostConstruct;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;

/**
 * Base class for identity-related processors that provides common functionality.
 *
 * <AUTHOR>
 */
public abstract class BaseIdentityProcessor extends AbstractRequestProcessor {

    protected final ResponseUtils responseUtils;

    /**
     * Constructor for identity-related processors.
     *
     * @param responseUtils The response utilities for creating error responses
     */
    protected BaseIdentityProcessor(ResponseUtils responseUtils) {
        this.responseUtils = responseUtils;
    }

    /**
     * Logs initialization to confirm dependency injection is working.
     */
    @PostConstruct
    public void init() {
        LoggingUtils.info("{} initialized", getName());
    }

    /**
     * Creates an error response for web client errors.
     *
     * @param headers The request headers
     * @param resource The resource being processed
     * @param status The HTTP status code
     * @param errorCode The error code
     * @param errorMessage The error message
     * @return A Mono that emits a response entity with an appropriate error message
     */
    protected Mono<ResponseEntity<byte[]>> createErrorResponse(
            HttpHeaders headers, Resource resource, HttpStatus status, String errorCode, String errorMessage) {
        return responseUtils.errorResponseReactive(headers, resource, status, errorCode, errorMessage);
    }
}
