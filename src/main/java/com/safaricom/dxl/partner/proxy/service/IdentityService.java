package com.safaricom.dxl.partner.proxy.service;


import com.safaricom.dxl.partner.proxy.model.UserInfoResponse;
import com.safaricom.dxl.partner.proxy.model.UserProfile;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */

public interface IdentityService {
    Mono<UserInfoResponse> getUserInfo(HttpHeaders headers);

    Mono<UserProfile> getUserProfile(HttpHeaders headers);
}
