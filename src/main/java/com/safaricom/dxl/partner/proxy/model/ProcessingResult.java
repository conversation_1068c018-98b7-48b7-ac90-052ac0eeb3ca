package com.safaricom.dxl.partner.proxy.model;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.ResponseEntity;

/**
 * Represents the result of a processing step in the request pipeline.
 * It can either indicate to continue processing or terminate with a response.
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ProcessingResult {
    private final boolean continueProcessing;
    private final ResponseEntity<byte[]> response;

    /**
     * Creates a result indicating that processing should continue to the next processor.
     *
     * @return A ProcessingResult that signals to continue processing
     */
    public static ProcessingResult continueProcessing() {
        return new ProcessingResult(true, null);
    }

    /**
     * Creates a result indicating that processing should terminate with the given response.
     *
     * @param response The response to return to the client
     * @return A ProcessingResult that signals to terminate processing
     */
    public static ProcessingResult terminate(ResponseEntity<byte[]> response) {
        return new ProcessingResult(false, response);
    }
}
