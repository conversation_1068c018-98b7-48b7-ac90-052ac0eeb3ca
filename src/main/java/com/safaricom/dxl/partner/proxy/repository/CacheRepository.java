package com.safaricom.dxl.partner.proxy.repository;

import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Objects;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.deserializeJson;
import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.serializeJson;


/**
 * <AUTHOR>
 */

@Repository
@RedisHash
public class CacheRepository {

    private final ReactiveValueOperations<String, Object> valueOperations;

    public CacheRepository(ReactiveRedisTemplate<String, Object> redisTemplate) {
        this.valueOperations = redisTemplate.opsForValue();
    }

    // JSON Methods
    public <T> Mono<Boolean> setData(String key, T value, Duration duration) {
        return valueOperations.set(key, Objects.requireNonNull(serializeJson(value)), duration);
    }

    public <T> Mono<T> getData(String key, Class<T> clazz) {
        return valueOperations.get(key).mapNotNull(json -> deserializeJson(json.toString(), clazz));
    }

    public Mono<Boolean> cacheData(String key, String value, Duration duration) {
        return valueOperations.set(key, Objects.requireNonNull(value), duration);
    }
    public Mono<String> getCachedData(String key) {
        return valueOperations.get(key).mapNotNull(Object::toString);
    }

    public Mono<Boolean> delete(String key) {
        return valueOperations.delete(key);
    }
}
