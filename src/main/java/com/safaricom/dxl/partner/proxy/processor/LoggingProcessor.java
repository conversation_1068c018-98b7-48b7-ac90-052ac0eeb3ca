package com.safaricom.dxl.partner.proxy.processor;


import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.UUID;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;

/**
 * Example processor that logs request details and adds a conversation ID if missing.
 * This demonstrates how to create a custom processor.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class LoggingProcessor extends AbstractRequestProcessor {

    @Override
    protected int getDefaultOrder() {
        return 10; // Execute very early in the pipeline
    }

    /**
     * Logs initialization to confirm dependency injection is working.
     */
    @PostConstruct
    public void init() {
        LoggingUtils.info("LoggingProcessor initialized");
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpHeaders headers = context.getHeaders();
        String method = context.getMethod().toString();
        String path = context.getPath();
        Resource resource = context.getResource();

        // Generate a conversation ID if missing
        if (!headers.containsKey(X_CONVERSATION_ID)) {
            String conversationId = UUID.randomUUID().toString();
            headers.set(X_CONVERSATION_ID, conversationId);
            WsLogManager.logger.debug("Added conversation ID: {}", conversationId);
        }

        // Log request details
        LoggingUtils.info("Incoming Request: Conversation ID: {} | Method: {} | Path: {} | Operation: {} | Microservice: {}",
                headers.getFirst(X_CONVERSATION_ID),
                method, path,
                resource.getOperation(),
                resource.getMicroservice() != null ? resource.getMicroservice() : "Unknown"
                );

        // Continue processing
        return Mono.just(ProcessingResult.continueProcessing());
    }
}
