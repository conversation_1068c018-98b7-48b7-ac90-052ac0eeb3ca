package com.safaricom.dxl.partner.proxy.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration properties for request processors.
 * Allows enabling/disabling processors and configuring their behavior.
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dxl.ms.processors")
public class ProcessorConfig {

    /**
     * Map of processor names to their enabled status.
     * If a processor is not in this map, it is considered enabled by default.
     */
    private Map<String, Boolean> enabled = new HashMap<>();

    /**
     * Map of processor names to their order values.
     * Lower values indicate higher priority.
     * If a processor is not in this map, it will use its default order.
     */
    private Map<String, Integer> order = new HashMap<>();

    /**
     * Map of processor names to their settings.
     * This allows for processor-specific configuration.
     */
    private Map<String, Map<String, String>> settings = new HashMap<>();

    /**
     * Map of processor names to their caching settings.
     * This allows for processor-specific configuration.
     */
    private Map<String, Map<String, Map<String, String>>> configs = new HashMap<>();


    /**
     * Checks if a processor is enabled.
     *
     * @param processorName The name of the processor to check
     * @return true if the processor is enabled, false otherwise
     */
    public boolean isEnabled(String processorName) {
        return enabled.getOrDefault(processorName, true);
    }

    /**
     * Gets the order value for a processor.
     *
     * @param processorName The name of the processor
     * @return The order value, or null if not specified
     */
    public Integer getOrder(String processorName) {
        return order.get(processorName);
    }

    /**
     * Gets a setting for a processor.
     *
     * @param processorName The name of the processor
     * @param settingName The name of the setting
     * @return The setting value, or null if not specified
     */
    public String getSetting(String processorName, String settingName) {
        Map<String, String> processorSettings = settings.get(processorName);
        return processorSettings != null ? processorSettings.get(settingName) : null;
    }

    public Map<String, String> getConfigs(String processorName, String configName) {
        Map<String, Map<String, String>> processorCacheSettings = configs.get(processorName);
        return processorCacheSettings != null ? processorCacheSettings.get(configName) : null;
    }

    // Environment-specific settings method removed in favor of simpler configuration
}
