package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.generateId;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.ERR_SUCCESS;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.POST_USER_LOGOUT;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;

/**
 * Processor that deletes user profile data from cache on logout operation.
 * <p>
 * Runs after AuthorizationProcessor and before UserProfileCachingProcessor.
 */
@Component
@RequiredArgsConstructor
public class UserLogoutProcessor extends AbstractRequestProcessor {
    private final CacheRepository cacheRepository;
    private final WsStarterService starterProperties;
    private final ResponseUtils responseUtils;

    @Override
    protected int getDefaultOrder() {
        return 41; // After UserLoginProcessor (40), before AuthenticationProcessor (50)
    }

    @PostConstruct
    public void init() {
        LoggingUtils.info("UserLogoutProcessor initialized");
    }

    @Override
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        // Use the shared utility for operation-based processing
        return shouldProcessBasedOnOperation(context, POST_USER_LOGOUT, true);
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpHeaders headers = context.getExchange().getRequest().getHeaders();
        String token = headers.getFirst(X_IDENTITY);
        if (token == null) {
            LoggingUtils.warn("UserLogoutProcessor: No token found in headers, skipping cache deletion");
            return Mono.just(ProcessingResult.continueProcessing());
        }
        String key = "ProxyAuth-" + starterProperties.hashText(headers.getFirst(X_IDENTITY));
        String cacheKey = generateId(token);
        // Delete both user, email and phone cache entries
        return cacheRepository.delete(key)
                .then(cacheRepository.delete("phone" + cacheKey))
                .then(cacheRepository.delete(cacheKey))
                .doOnSuccess(v -> LoggingUtils.info("UserLogoutProcessor: Deleted user profile cache for logout, keys: {} | {} | {}", key, cacheKey, "phone" + cacheKey))
                .flatMap(v -> handleResponse(headers, context.getResource(), null))
                .onErrorResume(e -> {
                            String errorMessage = "Failed to delete user profile cache for logout. Error: {}";
                            LoggingUtils.warn(errorMessage, e);
                            return handleResponse(headers, context.getResource(), e.getMessage());
                        }
                );
    }

    private Mono<ProcessingResult> handleResponse(HttpHeaders headers, Resource resource, String message) {
        return responseUtils.errorResponseReactive(headers, resource, HttpStatus.OK, ERR_SUCCESS, message)
                .map(ProcessingResult::terminate);

    }
}
