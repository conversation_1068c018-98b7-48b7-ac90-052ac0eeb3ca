package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;

/**
 * Example processor that demonstrates how to use configuration settings.
 * This processor adds a timeout to requests based on configuration.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TimeoutProcessor extends AbstractRequestProcessor {

    private final ResponseUtils responseUtils;

    @Override
    protected int getDefaultOrder() {
        return 90; // Execute just before request forwarding
    }

    /**
     * Logs initialization to confirm dependency injection is working.
     */
    @PostConstruct
    public void init() {
        LoggingUtils.info("TimeoutProcessor initialized");
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        // Get the timeout setting, with a default of 30 seconds
        String timeoutSetting = getSetting("timeoutSeconds", "30");

        // Parse the timeout value - this is now effectively final
        final long timeoutSeconds = Long.parseLong(timeoutSetting);

        LoggingUtils.debug("Setting request timeout to {} seconds", timeoutSeconds);

        // Store the timeout in the context for use by other processors
        context.setAttribute("requestTimeoutSeconds", timeoutSeconds);

        // Add a timeout to the request processing
        return Mono.just(ProcessingResult.continueProcessing())
                .timeout(Duration.ofSeconds(timeoutSeconds))
                .onErrorResume(e -> {
                    LoggingUtils.error("Request timed out after {} seconds", timeoutSeconds);
                    return responseUtils.errorResponseReactive(
                            context.getHeaders(),
                            context.getResource(),
                            HttpStatus.GATEWAY_TIMEOUT,
                            "GW_ERR4",
                            "Request timed out after " + timeoutSeconds + " seconds"
                    ).map(ProcessingResult::terminate);
                })
                .subscribeOn(Schedulers.boundedElastic());
    }
}
