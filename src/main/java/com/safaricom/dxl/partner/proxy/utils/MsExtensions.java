package com.safaricom.dxl.partner.proxy.utils;

import org.springframework.http.HttpHeaders;

/**
 * <AUTHOR>
 */

public class MsExtensions {
    private MsExtensions() {
    }

    public static void removeIdentityHeaders(HttpHeaders httpHeaders) {
        httpHeaders.remove("x-identity");
        httpHeaders.remove("x-identity-realm");
        httpHeaders.remove("x-identity-client");
        httpHeaders.remove("x-identity-client-id");
        httpHeaders.remove("x-identity-tenant");
    }

    public static void setSecurityHeaders(HttpHeaders httpHeaders) {
        httpHeaders.set("X-XSS-Protection", "1; mode=block");
        httpHeaders.set("X-Frame-Options", "DENY");
        httpHeaders.set("Content-Security-Policy", "script-src 'self'");
        httpHeaders.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        httpHeaders.set("X-Content-Type-Options", "nosniff");
    }
}
