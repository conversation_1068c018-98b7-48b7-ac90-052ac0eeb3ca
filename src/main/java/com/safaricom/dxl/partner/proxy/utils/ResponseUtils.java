package com.safaricom.dxl.partner.proxy.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.getProcessParams;
import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.httpHeadersToMap;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.NULL;

/**
 * Component for creating response entities using reactive patterns.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ResponseUtils {

    private final ObjectMapper objectMapper;
    private final WsResponseMapper responseMapper;


    /**
     * Creates an error response with the given parameters using reactive programming.
     *
     * @param headers The request headers
     * @param resource The resource being processed (can be null)
     * @param status The HTTP status code
     * @param code The error code
     * @param message The error message
     * @return A Mono that emits a ResponseEntity containing the error response
     */
    public Mono<ResponseEntity<byte[]>> errorResponseReactive(HttpHeaders headers, Resource resource,
                                                           HttpStatus status, String code, String message) {
        return Mono.fromCallable(() -> {
            try {
                return ResponseEntity.status(status).body(
                        objectMapper.writeValueAsBytes(
                                responseMapper.setApiResponse(
                                        getProcessParams(resource),
                                        code,
                                        NULL,
                                        message,
                                        FALSE,
                                        httpHeadersToMap(headers)
                                )
                        )
                );
            } catch (JsonProcessingException e) {
                LoggingUtils.error("Error creating error response: {}", e.getMessage());
                return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(new byte[0]);
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }

    public ResponseEntity<byte[]> errorResponse(HttpHeaders headers, Resource resource,
                                                              HttpStatus status, String code, String message) {
        try {
            return ResponseEntity.status(status).body(
                    objectMapper.writeValueAsBytes(
                            responseMapper.setApiResponse(
                                    getProcessParams(resource),
                                    code,
                                    NULL,
                                    message,
                                    FALSE,
                                    httpHeadersToMap(headers)
                            )
                    )
            );
        } catch (JsonProcessingException e) {
            LoggingUtils.error("Error creating error response: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(new byte[0]);
        }
    }
}
