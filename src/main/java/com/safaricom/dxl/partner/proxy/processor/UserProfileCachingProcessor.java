package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.UserProfileConfig;
import com.safaricom.dxl.partner.proxy.model.CachedUserProfile;
import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.UserProfile;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Objects;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.generateId;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.X_TOKEN;

/**
 * Processor that caches user profile data.
 * This processor runs after AuthorizationProcessor and caches selected user profile attributes
 * with an expiry configured in UserProfileConfig.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class UserProfileCachingProcessor extends AbstractRequestProcessor {
    private final CacheRepository cacheRepository;
    private final UserProfileConfig userProfileConfig;

    @Override
    protected int getDefaultOrder() {
        return 70; // Execute after authorization (order 60)
    }

    /**
     * Logs initialization to confirm dependency injection is working.
     */
    @PostConstruct
    public void init() {
        LoggingUtils.info("UserProfileCachingProcessor initialized");
    }

    /**
     * This method determines whether the processor should run.
     * We always return true and let the process method decide whether to continue or not.
     */
    @Override
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        // Always return true and let the process method decide whether to continue or not
        return true;
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        // Check if we have the required data to proceed
        if (!hasRequiredData(context)) {
            return Mono.just(ProcessingResult.continueProcessing());
        }

        // Get the token from headers
        String token = extractToken(context.getHeaders());
        if (token == null) {
            return Mono.just(ProcessingResult.continueProcessing());
        }
        // Remove Bearer prefix if it exists
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        // Create the cached user profile object
        CachedUserProfile cachedUserProfile = createCachedUserProfile(context.getUserProfile());

        boolean hasEmail = cachedUserProfile.getEmail() != null && !cachedUserProfile.getEmail().isEmpty();
        boolean hasPhone = cachedUserProfile.getPhone() != null && !cachedUserProfile.getPhone().isEmpty();

        if (!hasEmail && !hasPhone) {
            LoggingUtils.warn("No valid data found in user profile to cache, skipping caching");
            return Mono.just(ProcessingResult.continueProcessing());
        }

        Mono<Void> emailCacheMono = Mono.empty();
        Mono<Void> phoneCacheMono = Mono.empty();

        if (hasEmail) {
            emailCacheMono = performEmailCaching(token, cachedUserProfile.getEmail());
        }
        if (hasPhone) {
            phoneCacheMono = performPhoneCaching(token, cachedUserProfile.getPhone());
        }

        // Run both caching operations independently and continue processing after both complete
        return Mono.when(emailCacheMono, phoneCacheMono)
                .thenReturn(ProcessingResult.continueProcessing());
    }

    /**
     * Checks if the context has the required data for caching.
     *
     * @param context The request context
     * @return true if the context has the required data, false otherwise
     */
    private boolean hasRequiredData(RequestContext context) {
        if (context.getUserProfile() == null) {
            LoggingUtils.warn("User profile not available, cannot cache user profile data");
            return false;
        }
        return true;
    }

    /**
     * Extracts the token from the headers.
     *
     * @param headers The HTTP headers
     * @return The token, or null if not found
     */
    private String extractToken(HttpHeaders headers) {
        try {
            return Objects.requireNonNull(headers.getFirst(X_TOKEN)).trim();
        } catch (NullPointerException e) {
            LoggingUtils.warn("Token not found in headers, cannot cache user profile data");
            return null;
        }
    }



    /**
     * Performs the caching operation for email.
     *
     * @param token The token used to generate the cache key
     * @param email The email to cache
     * @return A Mono that completes when caching is done
     */
    private Mono<Void> performEmailCaching(String token, String email) {
        String cacheKey = generateId(token);
        Duration cacheDuration = getCacheDuration();
        return cacheRepository.getCachedData(cacheKey)
                .flatMap(existingEmailCache -> {
                    LoggingUtils.info("User email already cached for key: {}", cacheKey);
                    return Mono.just(true); // Prevent switchIfEmpty from triggering
                })
                .switchIfEmpty(Mono.defer(() ->
                        cacheRepository.cacheData(cacheKey, email, cacheDuration)
                                .doOnSuccess(success -> {
                                    if (Boolean.TRUE.equals(success)) {
                                        LoggingUtils.info("Successfully cached user email for key: {}", cacheKey);
                                    } else {
                                        LoggingUtils.warn("Failed to cache user email for key: {}", cacheKey);
                                    }
                                })
                                .doOnError(e -> LoggingUtils.error("Error caching user email: {}", e.getMessage()))
                                .onErrorResume(e -> Mono.empty())
                ))
                .then();
    }

    /**
     * Performs the caching operation for phone.
     *
     * @param token The token used to generate the cache key
     * @param phone The phone to cache
     * @return A Mono that completes when caching is done
     */
    private Mono<Void> performPhoneCaching(String token, String phone) {
        String cacheKey = "phone" + generateId(token);
        Duration cacheDuration = getCacheDuration();
        return cacheRepository.getCachedData(cacheKey)
                .flatMap(existingPhoneCache -> {
                    LoggingUtils.info("User phone already cached for key: {}", cacheKey);
                    return Mono.just(true); // Prevent switchIfEmpty from triggering
                })
                .switchIfEmpty(Mono.defer(() ->
                        cacheRepository.cacheData(cacheKey, phone, cacheDuration)
                                .doOnSuccess(success -> {
                                    if (Boolean.TRUE.equals(success)) {
                                        LoggingUtils.info("Successfully cached user phone for key: {}", cacheKey);
                                    } else {
                                        LoggingUtils.warn("Failed to cache user phone for key: {}", cacheKey);
                                    }
                                })
                                .doOnError(e -> LoggingUtils.error("Error caching user phone: {}", e.getMessage()))
                                .onErrorResume(e -> Mono.empty())
                ))
                .then();
    }

    /**
     * Gets the cache duration from the configuration.
     *
     * @return The cache duration
     */
    private Duration getCacheDuration() {
        String cacheDurationStr = userProfileConfig.getCacheDuration();
        try {
            if (cacheDurationStr == null || cacheDurationStr.isEmpty()) {
                LoggingUtils.warn("Cache duration is null or empty, using default of 1 hour");
                return Duration.ofHours(1);
            } else {
                return Duration.parse(cacheDurationStr);
            }
        } catch (Exception e) {
            LoggingUtils.warn("Error parsing cache duration '{}', using default of 1 hour: {}",
                    cacheDurationStr, e.getMessage());
            return Duration.ofHours(1);
        }
    }

    /**
     * Creates a CachedUserProfile object from the UserProfile.
     * Uses defensive programming to handle null values.
     *
     * @param userProfile The user profile to extract data from
     * @return A CachedUserProfile containing the data to be cached
     */
    private CachedUserProfile createCachedUserProfile(UserProfile userProfile) {
        if (userProfile == null) {
            LoggingUtils.warn("User profile is null, creating empty cached profile");
            return new CachedUserProfile(null, null);
        }

        return CachedUserProfile.builder()
                .email(userProfile.getEmail())
                .phone(userProfile.getPhoneNumber())
                .build();
    }
}
