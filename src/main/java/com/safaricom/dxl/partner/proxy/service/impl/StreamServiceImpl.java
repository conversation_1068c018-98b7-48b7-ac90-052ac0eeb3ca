package com.safaricom.dxl.partner.proxy.service.impl;

import com.safaricom.dxl.partner.proxy.model.Activity;
import com.safaricom.dxl.partner.proxy.service.StreamService;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.webflux.starter.service.WsStarterStreamProducer;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.serializeJson;

/**
 * Implementation of the StreamService that publishes activities to Kafka.
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class StreamServiceImpl implements StreamService {

    @Value("${dxl.kafka.topic}")
    private String topic;

    private final WsStarterStreamProducer streamProducer;

    @Override
    public Mono<Void> publish(Activity activity) {
        return Mono.fromRunnable(() -> {
            String serializedActivity = serializeActivity(activity);
            if (serializedActivity != null) {
                streamProducer.produce(topic, serializedActivity);
                LoggingUtils.debug("Published activity to topic {}: {}", topic, activity.getOperation());
            } else {
                LoggingUtils.error("Failed to serialize activity: {}", activity);
            }
        }).subscribeOn(Schedulers.boundedElastic())
          .then();
    }

    protected String serializeActivity(Activity activity) {
        return serializeJson(activity);
    }
}
