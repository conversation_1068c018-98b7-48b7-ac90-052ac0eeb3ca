package com.safaricom.dxl.partner.proxy.utils;

import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import org.slf4j.Logger;

import java.util.function.Supplier;

/**
 * Utility class for conditional logging.
 * This class wraps the WsLogManager.logger and adds conditional checks
 * to avoid unnecessary string concatenation and parameter evaluation.
 *
 * <AUTHOR>
 */
public class LoggingUtils {
    
    private static final Logger logger = WsLogManager.logger;
    
    private LoggingUtils() {
        // Private constructor to prevent instantiation
    }
    
    /**
     * Logs a message at DEBUG level if DEBUG is enabled.
     *
     * @param message The message to log
     */
    public static void debug(String message) {
        if (logger.isDebugEnabled()) {
            logger.debug(message);
        }
    }
    
    /**
     * Logs a message with one parameter at DEBUG level if DEBUG is enabled.
     *
     * @param format The message format with one placeholder
     * @param arg The parameter
     */
    public static void debug(String format, Object arg) {
        if (logger.isDebugEnabled()) {
            logger.debug(format, arg);
        }
    }
    
    /**
     * Logs a message with two parameters at DEBUG level if DEBUG is enabled.
     *
     * @param format The message format with two placeholders
     * @param arg1 The first parameter
     * @param arg2 The second parameter
     */
    public static void debug(String format, Object arg1, Object arg2) {
        if (logger.isDebugEnabled()) {
            logger.debug(format, arg1, arg2);
        }
    }
    
    /**
     * Logs a message with multiple parameters at DEBUG level if DEBUG is enabled.
     *
     * @param format The message format with placeholders
     * @param args The parameters
     */
    public static void debug(String format, Object... args) {
        if (logger.isDebugEnabled()) {
            logger.debug(format, args);
        }
    }
    
    /**
     * Logs a message with a lazily evaluated parameter at DEBUG level if DEBUG is enabled.
     *
     * @param format The message format with one placeholder
     * @param argSupplier Supplier for the parameter, only evaluated if DEBUG is enabled
     */
    public static void debugLazy(String format, Supplier<Object> argSupplier) {
        if (logger.isDebugEnabled()) {
            logger.debug(format, argSupplier.get());
        }
    }
    
    /**
     * Logs a message at INFO level if INFO is enabled.
     *
     * @param message The message to log
     */
    public static void info(String message) {
        if (logger.isInfoEnabled()) {
            logger.info(message);
        }
    }
    
    /**
     * Logs a message with one parameter at INFO level if INFO is enabled.
     *
     * @param format The message format with one placeholder
     * @param arg The parameter
     */
    public static void info(String format, Object arg) {
        if (logger.isInfoEnabled()) {
            logger.info(format, arg);
        }
    }
    
    /**
     * Logs a message with two parameters at INFO level if INFO is enabled.
     *
     * @param format The message format with two placeholders
     * @param arg1 The first parameter
     * @param arg2 The second parameter
     */
    public static void info(String format, Object arg1, Object arg2) {
        if (logger.isInfoEnabled()) {
            logger.info(format, arg1, arg2);
        }
    }
    
    /**
     * Logs a message with multiple parameters at INFO level if INFO is enabled.
     *
     * @param format The message format with placeholders
     * @param args The parameters
     */
    public static void info(String format, Object... args) {
        if (logger.isInfoEnabled()) {
            logger.info(format, args);
        }
    }
    
    /**
     * Logs a message at WARN level if WARN is enabled.
     *
     * @param message The message to log
     */
    public static void warn(String message) {
        if (logger.isWarnEnabled()) {
            logger.warn(message);
        }
    }
    
    /**
     * Logs a message with one parameter at WARN level if WARN is enabled.
     *
     * @param format The message format with one placeholder
     * @param arg The parameter
     */
    public static void warn(String format, Object arg) {
        if (logger.isWarnEnabled()) {
            logger.warn(format, arg);
        }
    }
    
    /**
     * Logs a message with two parameters at WARN level if WARN is enabled.
     *
     * @param format The message format with two placeholders
     * @param arg1 The first parameter
     * @param arg2 The second parameter
     */
    public static void warn(String format, Object arg1, Object arg2) {
        if (logger.isWarnEnabled()) {
            logger.warn(format, arg1, arg2);
        }
    }
    
    /**
     * Logs a message with multiple parameters at WARN level if WARN is enabled.
     *
     * @param format The message format with placeholders
     * @param args The parameters
     */
    public static void warn(String format, Object... args) {
        if (logger.isWarnEnabled()) {
            logger.warn(format, args);
        }
    }
    
    /**
     * Logs a message at ERROR level if ERROR is enabled.
     *
     * @param message The message to log
     */
    public static void error(String message) {
        if (logger.isErrorEnabled()) {
            logger.error(message);
        }
    }
    
    /**
     * Logs a message with one parameter at ERROR level if ERROR is enabled.
     *
     * @param format The message format with one placeholder
     * @param arg The parameter
     */
    public static void error(String format, Object arg) {
        if (logger.isErrorEnabled()) {
            logger.error(format, arg);
        }
    }
    
    /**
     * Logs a message with two parameters at ERROR level if ERROR is enabled.
     *
     * @param format The message format with two placeholders
     * @param arg1 The first parameter
     * @param arg2 The second parameter
     */
    public static void error(String format, Object arg1, Object arg2) {
        if (logger.isErrorEnabled()) {
            logger.error(format, arg1, arg2);
        }
    }
    
    /**
     * Logs a message with multiple parameters at ERROR level if ERROR is enabled.
     *
     * @param format The message format with placeholders
     * @param args The parameters
     */
    public static void error(String format, Object... args) {
        if (logger.isErrorEnabled()) {
            logger.error(format, args);
        }
    }
    
    /**
     * Logs a message with a throwable at ERROR level if ERROR is enabled.
     *
     * @param format The message format with placeholders
     * @param arg1 The first parameter
     * @param arg2 The second parameter
     * @param throwable The throwable to log
     */
    public static void error(String format, Object arg1, Object arg2, Throwable throwable) {
        if (logger.isErrorEnabled()) {
            logger.error(format, arg1, arg2, throwable);
        }
    }
}
