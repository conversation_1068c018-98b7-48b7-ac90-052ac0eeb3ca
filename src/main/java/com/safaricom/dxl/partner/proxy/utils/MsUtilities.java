package com.safaricom.dxl.partner.proxy.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.webflux.starter.enums.WsProcessLogger;
import lombok.SneakyThrows;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.REQUEST_PROCESSING;

/**
 * Utility class providing both synchronous and reactive methods for common operations.
 *
 * <AUTHOR>
 */
public class MsUtilities {

    private MsUtilities() {
    }

    public static String serializeJson(Object object) {
        try {
            var objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            WsProcessLogger.ERROR.log("Error serializing object to JSON | ".concat(e.getMessage()));
            return null;
        }
    }

    public static <T> T deserializeJson(String json, Class<T> clazz) {
        try {
            var objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            WsProcessLogger.ERROR.log("Error deserializing JSON to object | ".concat(e.getMessage()));
            return null;
        }
    }

    public static <T> List<T> deserializeJsonList(String json, Class<T> clazz) {
        try {
            var objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            var type = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
            return objectMapper.readValue(json, type);
        } catch (JsonProcessingException e) {
            WsProcessLogger.ERROR.log("Error deserializing JSON to list | ".concat(e.getMessage()));
            return Collections.emptyList();
        }
    }

    public static <K, V> Map<K, V> deserializeJsonMap(String json, Class<K> keyClass, Class<V> valueClass) {
        try {
            var objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            var type = objectMapper.getTypeFactory().constructMapType(Map.class, keyClass, valueClass);
            return objectMapper.readValue(json, type);
        } catch (JsonProcessingException e) {
            WsProcessLogger.ERROR.log("Error deserializing JSON to map | ".concat(e.getMessage()));
            return Collections.emptyMap();
        }
    }

    public static Map<String, String> httpHeadersToMap(HttpHeaders headers) {
        return headers.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> String.join(",", entry.getValue())
                ));
    }

    public static String generateId(String value){
        return value != null ? String.valueOf(UUID.nameUUIDFromBytes(value.getBytes())) : null;
    }

    @SneakyThrows
    public static String getProcessParams(Resource resource) {
        if (resource == null) {
            return REQUEST_PROCESSING + ",RESOURCE_VALIDATION,NOT_FOUND";
        }
        return REQUEST_PROCESSING + "," + resource.getOperation() + "," + new URI(resource.getEndpoint()).getHost();
    }

    public static String extractUsernameFromEmail(String email) {
        return email.split("@")[0];
    }

    /**
     * Reactive version of serializeJson that uses a dedicated thread pool.
     *
     * @param object The object to serialize
     * @return A Mono that emits the serialized JSON string
     */
    public static Mono<String> serializeJsonReactive(Object object) {
        return Mono.fromCallable(() -> serializeJson(object))
                .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * Reactive version of deserializeJson that uses a dedicated thread pool.
     *
     * @param json The JSON string to deserialize
     * @param clazz The class to deserialize to
     * @param <T> The type of the class
     * @return A Mono that emits the deserialized object
     */
    public static <T> Mono<T> deserializeJsonReactive(String json, Class<T> clazz) {
        return Mono.fromCallable(() -> deserializeJson(json, clazz))
                .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * Reactive version of deserializeJsonList that uses a dedicated thread pool.
     *
     * @param json The JSON string to deserialize
     * @param clazz The class of the list elements
     * @param <T> The type of the list elements
     * @return A Mono that emits the deserialized list
     */
    public static <T> Mono<List<T>> deserializeJsonListReactive(String json, Class<T> clazz) {
        return Mono.fromCallable(() -> deserializeJsonList(json, clazz))
                .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * Reactive version of deserializeJsonMap that uses a dedicated thread pool.
     *
     * @param json The JSON string to deserialize
     * @param keyClass The class of the map keys
     * @param valueClass The class of the map values
     * @param <K> The type of the map keys
     * @param <V> The type of the map values
     * @return A Mono that emits the deserialized map
     */
    public static <K, V> Mono<Map<K, V>> deserializeJsonMapReactive(String json, Class<K> keyClass, Class<V> valueClass) {
        return Mono.fromCallable(() -> deserializeJsonMap(json, keyClass, valueClass))
                .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * Reactive version of getProcessParams that uses a dedicated thread pool.
     *
     * @param resource The resource to get process parameters for
     * @return A Mono that emits the process parameters string
     */
    public static Mono<String> getProcessParamsReactive(Resource resource) {
        return Mono.fromCallable(() -> {
            try {
                if (resource == null) {
                    return REQUEST_PROCESSING + ",RESOURCE_VALIDATION,NOT_FOUND";
                }
                return REQUEST_PROCESSING + "," + resource.getOperation() + "," + new URI(resource.getEndpoint()).getHost();
            } catch (URISyntaxException e) {
                LoggingUtils.error("Error creating URI from endpoint: {}", e.getMessage());
                return REQUEST_PROCESSING + "," + resource.getOperation() + ",ERROR";
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }
}
