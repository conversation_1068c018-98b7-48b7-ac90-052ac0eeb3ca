package com.safaricom.dxl.partner.proxy.processor;


import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.service.ResourceRegistry;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.PathContainer;
import org.springframework.stereotype.Component;
import org.springframework.web.util.pattern.PathPattern;
import org.springframework.web.util.pattern.PathPatternParser;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * Processor that resolves the appropriate resource configuration for the request.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ResourceResolutionProcessor extends AbstractRequestProcessor {

    private final ResourceRegistry resourceRegistry;
    private final ResponseUtils responseUtils;

    @Override
    protected int getDefaultOrder() {
        return 1; // Execute first in the pipeline
    }

    /**
     * Constructor logs the number of resources available to confirm dependency injection is working.
     */
    @PostConstruct
    public void init() {
        LoggingUtils.info("ResourceResolutionProcessor initialized with {} resources", resourceRegistry.getResources().size());
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpMethod method = context.getMethod();
        String path = context.getPath();

        LoggingUtils.debug("Resolving resource for {} {}", method, path);

        Resource resource = findMatchingResource(method, path);
        if (Objects.isNull(resource)) {
            LoggingUtils.warn("No matching resource found for {} {}", method, path);
            return responseUtils.errorResponseReactive(context.getHeaders(), null, HttpStatus.BAD_GATEWAY, "GW_ERR1", method + ":" + path)
                    .map(ProcessingResult::terminate);
        }

        WsLogManager.logger.debug("Found matching resource: {}", resource.getOperation());
        context.setResource(resource);

        // Extract path variables and store them in the context
        PathPatternParser parser = new PathPatternParser();
        Map<String, String> pathVariables = extractPathVariables(parser, resource.getFullPath(), path);
        context.setAttribute("pathVariables", pathVariables);

        return Mono.just(ProcessingResult.continueProcessing());
    }

    /**
     * Finds a resource configuration that matches the given method and path.
     *
     * @param method The HTTP method
     * @param path The request path
     * @return The matching resource, or null if none is found
     */
    private Resource findMatchingResource(HttpMethod method, String path) {
        PathPatternParser parser = new PathPatternParser();
        return resourceRegistry.getResources().stream()
                .filter(config -> method.matches(config.getMethod()) && matchesPath(parser, config.getFullPath(), path))
                .findAny()
                .orElse(null);
    }

    /**
     * Checks if a path matches a pattern.
     *
     * @param parser The path pattern parser
     * @param template The pattern template
     * @param path The path to check
     * @return true if the path matches the pattern, false otherwise
     */
    private boolean matchesPath(PathPatternParser parser, String template, String path) {
        PathPattern pattern = parser.parse(template);
        return pattern.matches(PathContainer.parsePath(path));
    }

    /**
     * Extracts path variables from a path based on a pattern.
     *
     * @param parser The path pattern parser
     * @param template The pattern template
     * @param path The path to extract from
     * @return A map of path variable names to values
     */
    private Map<String, String> extractPathVariables(PathPatternParser parser, String template, String path) {
        PathPattern pattern = parser.parse(template);
        PathPattern.PathMatchInfo matchInfo = pattern.matchAndExtract(PathContainer.parsePath(path));
        return matchInfo != null ? matchInfo.getUriVariables() : Collections.emptyMap();
    }
}
