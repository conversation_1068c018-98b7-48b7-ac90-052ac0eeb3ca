package com.safaricom.dxl.partner.proxy.component;

import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@Component
@RequiredArgsConstructor
public class ConfigServerProperties {
    private final Environment environment;

    public String getUrl() {
        String activeProfile = Objects.requireNonNull(Optional.ofNullable(environment.getProperty("SPRING_PROFILES_ACTIVE"))
                .orElse(environment.getProperty("spring.profiles.active")));

        String configImport = Objects.requireNonNull(
                        Optional.ofNullable(environment.getProperty("SPRING_CONFIG_IMPORT"))
                                .orElse(environment.getProperty("spring.config.import")))
                .replace("optional:", "")
                .replace("configserver:", "")
                .trim();

        return configImport + "/partner-portal-proxy-resources/" + activeProfile;
    }

    public String getUsername() {
        return Objects.requireNonNull(Optional.ofNullable(environment.getProperty("SPRING_CLOUD_CONFIG_USERNAME"))
                .orElse(environment.getProperty("spring.cloud.config.username")));
    }

    public String getPassword() {
        return Objects.requireNonNull(Optional.ofNullable(environment.getProperty("SPRING_CLOUD_CONFIG_PASSWORD"))
                .orElse(environment.getProperty("spring.cloud.config.password")));
    }
}
