package com.safaricom.dxl.partner.proxy.service;

import com.safaricom.dxl.partner.proxy.config.BasicAuthConfig;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.ResourceConfig;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import lombok.Getter;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * Service that stores and provides access to validated resource configurations.
 * Uses an event-driven approach to receive configurations.
 *
 * <AUTHOR>
 */
@Service
public class ResourceRegistry {
    private static final String PLACEHOLDER = "placeholder";
    private static final String PLACEHOLDER_REGEX = "\\{[a-zA-Z_][\\w-]*}";

    private final BasicAuthConfig basicAuthConfig;
    @Getter
    private List<Resource> resources = new ArrayList<>();

    public ResourceRegistry(BasicAuthConfig basicAuthConfig) {
        this.basicAuthConfig = basicAuthConfig;
    }

    /**
     * Validates and stores the resource configurations.
     *
     * @param resourceConfigs The resource configurations to validate and store
     */
    public void validateAndStoreResources(List<ResourceConfig> resourceConfigs) {
        LoggingUtils.info("ResourceRegistry received {} resource configuration groups", resourceConfigs.size());
        validateGroups(resourceConfigs);

        resources = resourceConfigs
                .stream()
                .flatMap(group -> group.getResources().stream())
                .toList();

        LoggingUtils.info("ResourceRegistry now contains {} validated resources", resources.size());
        LoggingUtils.info("ResourceRegistry: Resources validated and stored successfully");
    }

    /**
     * Validates the resource configuration groups.
     *
     * @param resourceConfigs The resource configurations to validate
     * @throws IllegalArgumentException If validation fails
     */
    private void validateGroups(List<ResourceConfig> resourceConfigs) {
        Set<String> uniqueGroups = new HashSet<>();
        Set<String> uniqueOperations = new HashSet<>();
        Map<String, Set<String>> groupPaths = new HashMap<>();
        for (ResourceConfig group : resourceConfigs) {
            validateGroupName(group.getGroup());
            if (!uniqueGroups.add(group.getGroup())) {
                throw new IllegalArgumentException("Duplicate group found: " + group.getGroup());
            }
            for (Resource resource : group.getResources()) {
                validateResource(resource);
                resource.setFullPath("/" + group.getGroup() + resource.getPath());
                if (!uniqueOperations.add(resource.getOperation())) {
                    throw new IllegalArgumentException("Duplicate operation found: " + resource.getOperation());
                }
                String methodPathKey = resource.getMethod() + ":" + resource.getPath();
                groupPaths.computeIfAbsent(group.getGroup(), k -> new HashSet<>());
                if (!groupPaths.get(group.getGroup()).add(methodPathKey)) {
                    throw new IllegalArgumentException(
                            "Duplicate method+path combination found in group " + group.getGroup() + ": " + methodPathKey);
                }
            }
        }
    }

    /**
     * Validates a group name.
     *
     * @param groupName The group name to validate
     * @throws IllegalArgumentException If the group name is invalid
     */
    private void validateGroupName(String groupName) {
        if (!groupName.matches("^[a-zA-Z0-9-]+$")) {
            throw new IllegalArgumentException("Invalid group name: " + groupName +
                    ". Group names can only contain alphanumerics and '-'");
        }
    }

    /**
     * Validates a resource.
     *
     * @param resource The resource to validate
     * @throws IllegalArgumentException If the resource is invalid
     */
    private void validateResource(Resource resource) {
        if (!isValidMethod(resource.getMethod())) {
            throw new IllegalArgumentException("Invalid HTTP method: " + resource.getMethod());
        }

        if (!isValidPath(resource.getPath())) {
            throw new IllegalArgumentException("Invalid resource path: " + resource.getPath());
        }

        if (!isValidUrl(resource.getEndpoint())) {
            throw new IllegalArgumentException("Invalid endpoint URL: " + resource.getEndpoint());
        }

        if (!org.springframework.util.StringUtils.hasText(resource.getOperation())) {
            throw new IllegalArgumentException("Operation cannot be null or blank");
        }

        validateAuthType(resource.getAuthType());
        validateBasicAuthCredentials(resource.getBasicAuthCredentials());
    }

    /**
     * Validates an authentication type.
     *
     * @param authType The authentication type to validate
     * @throws IllegalArgumentException If the authentication type is invalid
     */
    private void validateAuthType(String authType) {
        if (!"basic".equalsIgnoreCase(authType) && !"bearer".equalsIgnoreCase(authType)) {
            throw new IllegalArgumentException("Invalid authType: " + authType +
                    ". Supported values are 'basic' or 'bearer'.");
        }
    }

    /**
     * Validates basic authentication credentials.
     *
     * @param basicAuthCredentials The basic authentication credentials to validate
     * @throws IllegalArgumentException If the credentials are invalid
     */
    private void validateBasicAuthCredentials(String basicAuthCredentials) {
        if (!basicAuthConfig.getCredentials().containsKey(basicAuthCredentials)) {
            throw new IllegalArgumentException("Invalid basicAuthCredentials: " + basicAuthCredentials +
                    ". Must match a key in basic auth credentials.");
        }
    }

    /**
     * Checks if an HTTP method is valid.
     *
     * @param method The HTTP method to check
     * @return true if the method is valid, false otherwise
     */
    private boolean isValidMethod(String method) {
        try {
            if (method == null) {
                return false;
            }
            return Arrays.stream(org.springframework.http.HttpMethod.values())
                    .anyMatch(permittedMethod -> permittedMethod.name().equalsIgnoreCase(method));
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Checks if a path is valid.
     *
     * @param path The path to check
     * @return true if the path is valid, false otherwise
     */
    private boolean isValidPath(String path) {
        String sanitizedPath = path.replaceAll(PLACEHOLDER_REGEX, PLACEHOLDER);
        String[] segments = sanitizedPath.split("/");
        for (String segment : segments) {
            if (segment.contains(PLACEHOLDER) && !segment.matches(PLACEHOLDER)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Checks if a URL is valid.
     *
     * @param url The URL to check
     * @return true if the URL is valid, false otherwise
     */
    private boolean isValidUrl(String url) {
        String sanitizedUrl = url.replaceAll(PLACEHOLDER_REGEX, PLACEHOLDER);
        try {
            URI uri = new URI(sanitizedUrl);
            String[] segments = uri.getPath().split("/");
            for (String segment : segments) {
                if (segment.contains(PLACEHOLDER) && !segment.matches(PLACEHOLDER)) {
                    return false;
                }
            }
            return true;
        } catch (URISyntaxException | IllegalArgumentException e) {
            return false;
        }
    }
}
