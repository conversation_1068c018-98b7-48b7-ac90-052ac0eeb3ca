package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import reactor.core.publisher.Mono;

/**
 * Interface for request processors in the processing pipeline.
 * Each processor is responsible for a specific aspect of request processing.
 *
 * <AUTHOR>
 */
public interface RequestProcessor {
    
    /**
     * Gets the name of the processor for logging and debugging purposes.
     *
     * @return The processor name
     */
    String getName();
    
    /**
     * Gets the order in which this processor should be executed in the pipeline.
     * Lower values indicate higher priority.
     *
     * @return The order value
     */
    int getOrder();
    
    /**
     * Determines whether this processor should process the given request context.
     * This allows for conditional processing based on request properties.
     *
     * @param context The request context
     * @return true if this processor should process the request, false otherwise
     */
    boolean shouldProcess(RequestContext context);
    
    /**
     * Processes the request context and returns a result indicating whether to continue
     * processing or terminate with a response.
     *
     * @param context The request context
     * @return A Mono that emits a ProcessingResult
     */
    Mono<ProcessingResult> process(RequestContext context);
}
