package com.safaricom.dxl.partner.proxy.controller;

import com.safaricom.dxl.partner.proxy.service.ProxyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */

@Tag(name = "View360 Proxy", description = "Controller")
@RestController
public class ProxyController {

    private final ProxyService proxyService;

    public ProxyController(ProxyService proxyService) {
        this.proxyService = proxyService;
    }

    @Operation(summary = "Process request")
    @RequestMapping(value = "/**")
    public Mono<ResponseEntity<byte[]>> processRequest(ServerWebExchange exchange, @RequestBody(required = false) Mono<byte[]> body) {
        return proxyService.processRequest(exchange, body);
    }
}