package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.UserProfileConfig;
import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.SessionDetails;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.POST_USER_LOGIN;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;

/**
 * Processor that caches user profile data on login operation.
 *
 * Runs after AuthorizationProcessor and before UserProfileCachingProcessor.
 */
@Component
@RequiredArgsConstructor
public class UserLoginProcessor extends AbstractRequestProcessor {
    private final CacheRepository cacheRepository;
    private final UserProfileConfig userProfileConfig;
    private final WsStarterService starterProperties;
    private final ResponseUtils responseUtils;

    @Override
    protected int getDefaultOrder() {
        return 40; // After UserProfileProcessor (30), before UserLogoutProcessor (41)
    }

    @PostConstruct
    public void init() {
        LoggingUtils.info("UserLoginProcessor initialized");
    }

    @Override
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        return shouldProcessBasedOnOperation(context, POST_USER_LOGIN, true);
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpHeaders headers = context.getExchange().getRequest().getHeaders();
        Mono<Void> userSessionMono;
        // Create session details with current timestamp
        SessionDetails sessionDetails = SessionDetails.builder()
                .sessionId(headers.getFirst(X_CONVERSATION_ID))
                .timestamp(LocalDateTime.now())
                .build();

        // Generate a unique key for caching
        String key = "ProxyAuth-"+starterProperties.hashText(headers.getFirst(X_IDENTITY));

        // Cache the user profile with the session details
        userSessionMono = cacheRepository.setData(key, sessionDetails, Duration.parse(userProfileConfig.getCacheDuration()))
                .then(Mono.fromRunnable(() -> LoggingUtils.info("UserLoginProcessor: Cached user session details for login, key: {}", key)));
        return Mono.when(userSessionMono)
                .thenReturn(ProcessingResult.terminate(responseUtils.errorResponse(headers, context.getResource(), HttpStatus.OK, "200", "User session cached successfully")))
                .onErrorResume(ex -> {
                    LoggingUtils.warn("UserLoginProcessor: Error caching user session details: {}", ex.getMessage());
                    return Mono.just(ProcessingResult.terminate(responseUtils.errorResponse(headers, context.getResource(), HttpStatus.SERVICE_UNAVAILABLE, "GW_ERR6",  ex.getMessage())));
                });
    }
}
