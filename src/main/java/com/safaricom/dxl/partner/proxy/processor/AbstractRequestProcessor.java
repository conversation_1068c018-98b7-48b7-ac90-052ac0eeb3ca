package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.ProcessorConfig;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;

import java.util.HashMap;
import java.util.Map;

/**
 * Base class for request processors that provides default implementations
 * for common methods.
 *
 * <AUTHOR>
 */
public abstract class AbstractRequestProcessor implements RequestProcessor {


    /**
     * Determines whether this processor should process the given request context,
     * based on the resource operation.
     *
     * @param context The request context
     * @param targetOperation The operation to check for (to process or to skip)
     * @param shouldProcessOperation Whether the processor should process the target operation (true)
     *                              or skip it (false)
     * @return true if this processor should process the request, false otherwise
     */
    protected boolean shouldProcessBasedOnOperation(RequestContext context, String targetOperation, boolean shouldProcessOperation) {
        var resource = context.getResource();
        if (resource == null) {
            return false;
        }
        boolean isTargetOperation = targetOperation.equals(resource.getOperation());
        return shouldProcessOperation == isTargetOperation;
    }

    @Setter(onMethod_ = {@Autowired(required = false)})
    private ProcessorConfig processorConfig;

    /**
     * Gets the name of the processor, which defaults to the class name.
     *
     * @return The processor name
     */
    @Override
    public String getName() {
        return getClass().getSimpleName();
    }

    /**
     * Gets the order in which this processor should be executed.
     * First checks if an order is specified in the configuration.
     * If not, returns the default order from {@link #getDefaultOrder()}.
     *
     * @return The order value
     */
    @Override
    public final int getOrder() {
        if (processorConfig != null) {
            Integer configOrder = processorConfig.getOrder(getName());
            if (configOrder != null) {
                return configOrder;
            }
        }
        return getDefaultOrder();
    }

    /**
     * Gets the default order for this processor.
     * Default implementation returns {@link Ordered#LOWEST_PRECEDENCE}.
     * Subclasses should override this to specify their default order.
     *
     * @return The default order value
     */
    protected int getDefaultOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }

    /**
     * Determines whether this processor should process the given request context.
     * First checks if the processor is enabled in the configuration.
     * If enabled, delegates to {@link #shouldProcessIfEnabled(RequestContext)}.
     *
     * @param context The request context
     * @return true if this processor should process the request, false otherwise
     */
    @Override
    public final boolean shouldProcess(RequestContext context) {
        // Check if the processor is enabled in the configuration
        if (processorConfig != null && !processorConfig.isEnabled(getName())) {
            return false;
        }

        // If enabled, delegate to the subclass implementation
        return shouldProcessIfEnabled(context);
    }

    /**
     * Determines whether this processor should process the given request context,
     * assuming the processor is enabled in the configuration.
     * Default implementation always returns true.
     * Subclasses can override this to implement conditional processing.
     *
     * @param context The request context
     * @return true if this processor should process the request, false otherwise
     */
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        return true;
    }

    /**
     * Gets a setting for this processor.
     *
     * @param settingName The name of the setting
     * @return The setting value, or null if not specified
     */
    protected String getSetting(String settingName) {
        if (processorConfig == null) {
            return null;
        }
        return processorConfig.getSetting(getName(), settingName);
    }

    /**
     * Gets a setting for this processor with a default value.
     *
     * @param settingName The name of the setting
     * @param defaultValue The default value to return if the setting is not specified
     * @return The setting value, or the default value if not specified
     */
    protected String getSetting(String settingName, String defaultValue) {
        String value = getSetting(settingName);
        LoggingUtils.debug("{} Settings: {}", getName(), value);
        return value != null ? value : defaultValue;
    }

    /**
     * Gets configuration map for this processor.
     *
     * @param configName The name of the config
     * @return The configuration map, or an empty map if not specified or if processorConfig is null
     */
    protected Map<String, String> getConfigs(String configName) {
        if (processorConfig == null) {
            LoggingUtils.warn("{} processorConfig is null, cannot get configs for {}", getName(), configName);
            return new HashMap<>();
        }

        try {
            Map<String, String> value = processorConfig.getConfigs(getName(), configName);
            if (value == null) {
                LoggingUtils.warn("{} No configuration found for {}", getName(), configName);
                return new HashMap<>();
            }
            LoggingUtils.info("{} Configs for {}: {}", getName(), configName, value);
            return value;
        } catch (Exception e) {
            LoggingUtils.error("{} Error retrieving configs for {}: {}", getName(), configName, e.getMessage());
            return new HashMap<>();
        }
    }

    // Environment-specific settings method removed in favor of simpler configuration
}
