package com.safaricom.dxl.partner.proxy.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */

@Data
public class UserProfile {
    @JsonProperty("sub")
    private String sub;

    @JsonProperty("realm_id")
    private String realmId;

    @JsonProperty("client_id")
    private String clientId;

    @JsonProperty("tenant_id")
    private String tenantId;

    @JsonProperty("tenant_name")
    private String tenantName;

    @JsonProperty("realm_name")
    private String realmName;

    @JsonProperty("given_name")
    private String givenName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("middle_name")
    private String middleName;

    @JsonProperty("family_name")
    private String familyName;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("is_owner")
    private boolean isOwner;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("preferred_username")
    private String preferredUsername;

    @JsonProperty("picture")
    private String picture;

    @JsonProperty("account_type")
    private String accountType;

    @JsonProperty("department")
    private String department;

    @JsonProperty("app_profile")
    private AppProfile appProfile;

    @JsonProperty("email")
    private String email;

    @JsonProperty("email_verified")
    private boolean emailVerified;

    @JsonProperty("phone_number")
    private String phoneNumber;

    @JsonProperty("phone_number_verified")
    private boolean phoneNumberVerified;

    @JsonProperty("username")
    private String username;

    @JsonProperty("roles")
    private List<Role> roles;

    @JsonProperty("groups")
    private List<String> groups;

    @JsonProperty("permissions")
    private List<String> permissions;

    @Data
    public static class AppProfile {
        @JsonProperty("department")
        private String department;
    }

    @Data
    public static class Role {
        @JsonProperty("id")
        private String id;

        @JsonProperty("name")
        private String name;
    }
}