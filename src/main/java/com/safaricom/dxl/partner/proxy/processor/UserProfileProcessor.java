package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.service.IdentityService;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.serializeJson;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;

/**
 * Processor that handles requests for user profile information.
 *
 * <AUTHOR>
 */
@Component
public class UserProfileProcessor extends BaseIdentityProcessor {

    private final IdentityService identityService;

    public UserProfileProcessor(IdentityService identityService, ResponseUtils responseUtils) {
        super(responseUtils);
        this.identityService = identityService;
    }

    @Override
    protected int getDefaultOrder() {
        return 30; // Execute after header validation but before login/authentication
    }

    // init() method inherited from BaseIdentityProcessor

    @Override
    protected boolean shouldProcessIfEnabled(RequestContext context) {
        return shouldProcessBasedOnOperation(context, GET_LOGGED_IN_USER_PROFILE, true);
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpHeaders headers = context.getHeaders();
        Resource resource = context.getResource();

        LoggingUtils.debug("Processing user profile request");

        return identityService.getUserInfo(headers)
                .flatMap(response -> {
                    byte[] responseBytes = Objects.requireNonNull(serializeJson(response))
                            .getBytes(StandardCharsets.UTF_8);

                    LoggingUtils.debug("User profile retrieved successfully");

                    return Mono.just(ProcessingResult.terminate(
                            ResponseEntity.ok().body(responseBytes)
                    ));
                })
                .onErrorResume(throwable -> {
                    LoggingUtils.error("Error retrieving user profile: {}", throwable.getMessage());

                    return handleWebClientError(throwable, headers, resource)
                            .map(ProcessingResult::terminate);
                });
    }

    /**
     * Handles errors that occur during user profile retrieval.
     *
     * @param throwable The error that occurred
     * @param headers The request headers
     * @param resource The resource being processed
     * @return A Mono that emits a response entity with an appropriate error message
     */
    private Mono<ResponseEntity<byte[]>> handleWebClientError(Throwable throwable, HttpHeaders headers, Resource resource) {
        // For simplicity, we'll just return a generic error
        // In a real implementation, you would handle different types of errors differently
        return createErrorResponse(headers, resource, HttpStatus.INTERNAL_SERVER_ERROR,
                "GW_ERR9", "Failed to retrieve user profile: " + throwable.getMessage());
    }
}
