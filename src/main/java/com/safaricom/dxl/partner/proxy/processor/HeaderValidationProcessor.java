package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.MsConfigProperties;
import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.ES;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_MSISDN;

/**
 * Processor that validates required headers based on resource configuration.
 *
 * <AUTHOR>
 */
@Component
public class HeaderValidationProcessor extends AbstractRequestProcessor {

    private final MsConfigProperties configProperties;
    private final ResponseUtils responseUtils;

    public HeaderValidationProcessor(MsConfigProperties configProperties, ResponseUtils responseUtils) {
        this.configProperties = configProperties;
        this.responseUtils = responseUtils;
    }

    @Override
    protected int getDefaultOrder() {
        return 20; // Execute after resource resolution
    }

    /**
     * Logs initialization to confirm dependency injection is working.
     */
    @PostConstruct
    public void init() {
        LoggingUtils.info("HeaderValidationProcessor initialized");
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        HttpHeaders headers = context.getHeaders();
        Resource resource = context.getResource();

        LoggingUtils.debug("Validating headers for resource: {}", resource.getOperation());

        // Validate MSISDN header
        Mono<ProcessingResult> msisdnValidation = validateMsisdnHeader(headers, resource);
        if (msisdnValidation != null) {
            return msisdnValidation;
        }
        // Continue processing
        return Mono.just(ProcessingResult.continueProcessing());

    }

    /**
     * Validates the MSISDN header based on resource configuration.
     *
     * @param headers The request headers
     * @param resource The resource configuration
     * @return A Mono that emits a termination result if validation fails, or null if validation passes
     */
    private Mono<ProcessingResult> validateMsisdnHeader(HttpHeaders headers, Resource resource) {
        String msisdn = headers.getFirst(X_MSISDN);

        if (Boolean.FALSE.equals(resource.getExemptMsisdnHeader())) {
            // MSISDN is required
            if (Objects.isNull(msisdn)) {
                LoggingUtils.warn("Missing required MSISDN header for resource: {}", resource.getOperation());
                return responseUtils.errorResponseReactive(headers, resource, HttpStatus.BAD_REQUEST, "IV_ERR1", ES)
                        .map(ProcessingResult::terminate);
            }

            // MSISDN must match regex
            if (StringUtils.isBlank(msisdn) || !msisdn.matches(configProperties.getMsisdnRegex())) {
                LoggingUtils.warn("Invalid MSISDN format: {} for resource: {}", msisdn, resource.getOperation());
                return responseUtils.errorResponseReactive(headers, resource, HttpStatus.BAD_REQUEST, "IV_ERR2", ES)
                        .map(ProcessingResult::terminate);
            }
        } else if (StringUtils.isNotBlank(msisdn) && !msisdn.matches(configProperties.getMsisdnRegex())) {
            // If MSISDN is optional but provided, it must still match regex
            // If it doesn't match, remove it
            LoggingUtils.debug("Removing invalid MSISDN header: {}", msisdn);
            headers.remove(X_MSISDN);
        }

        return null; // Validation passed
    }
}
