package com.safaricom.dxl.partner.proxy.service;

import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.processor.RequestProcessor;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Orchestrates the request processing pipeline by executing processors in order.
 *
 * <AUTHOR>
 */
@Service
public class RequestPipeline {
    private final List<RequestProcessor> sortedProcessors;

    protected List<RequestProcessor> getSortedProcessors() {
        return sortedProcessors;
    }

    public RequestPipeline(List<RequestProcessor> processors) {
        // Sort processors by their order value
        this.sortedProcessors = processors.stream()
                .sorted(Comparator.comparing(RequestProcessor::getOrder))
                .toList();

        // Log the processor chain for debugging
        LoggingUtils.info("Initialized request pipeline with {} processors: {}",
                sortedProcessors.size(),
                sortedProcessors.stream()
                        .map(p -> p.getName() + " (order=" + p.getOrder() + ")")
                        .collect(Collectors.joining(", ")));
    }

    /**
     * Processes a request through the pipeline.
     *
     * @param exchange The server web exchange
     * @param body The request body
     * @return A Mono that emits the response entity
     */
    public Mono<ResponseEntity<byte[]>> process(ServerWebExchange exchange, Mono<byte[]> body) {
        RequestContext context = new RequestContext(exchange, body);
        return processNext(context, 0);
    }

    /**
     * Recursively processes the next processor in the chain.
     *
     * @param context The request context
     * @param index The index of the next processor to execute
     * @return A Mono that emits the response entity
     */
    protected Mono<ResponseEntity<byte[]>> processNext(RequestContext context, int index) {
        // If we've processed all processors without a termination, return an error
        if (index >= sortedProcessors.size()) {
            return Mono.error(new IllegalStateException("Request processing completed without a response"));
        }

        RequestProcessor processor = sortedProcessors.get(index);

        // Skip processors that shouldn't process this request
        if (!processor.shouldProcess(context)) {
            LoggingUtils.debug("Skipping processor: {}", processor.getName());
            return processNext(context, index + 1);
        }

        LoggingUtils.debug("Executing processor: {}", processor.getName());

        // Process the request and handle the result
        return processor.process(context)
                .flatMap(result -> {
                    if (result.isContinueProcessing()) {
                        // Continue to the next processor
                        return processNext(context, index + 1);
                    } else {
                        // Terminate processing and return the response
                        LoggingUtils.debug("Processing terminated by: {}", processor.getName());
                        return Mono.just(result.getResponse());
                    }
                })
                .onErrorResume(error -> {
                    // Log processor errors and continue to the next processor
                    LoggingUtils.error("Error in processor {}: {}",
                            processor.getName(), error.getMessage(), error);
                    return processNext(context, index + 1);
                });
    }
}
