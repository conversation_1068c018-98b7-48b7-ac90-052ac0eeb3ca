package com.safaricom.dxl.partner.proxy.component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.ResourceConfig;
import com.safaricom.dxl.partner.proxy.service.ResourceRegistry;
import com.safaricom.dxl.partner.proxy.utils.LoggingUtils;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Component that loads resource configurations from the config server.
 * Uses reactive programming and event publishing for initialization.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ResourceConfigsLoader {

    private final ConfigServerProperties serverProperties;
    private final WebClient webClient;
    private final ResourceRegistry resourceRegistry;
    private final ApplicationEventPublisher eventPublisher;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Initializes the component by loading resource configurations from the config server.
     * Uses reactive programming and publishes events on success or failure.
     */
    @PostConstruct
    public void init() {
        fetchConfig()
                .doOnSuccess(resourceConfigs -> {
                    int groupCount = resourceConfigs.size();
                    int resourceCount = resourceConfigs.stream()
                            .mapToInt(config -> config.getResources().size())
                            .sum();

                    LoggingUtils.info("Successfully loaded {} resources in {} groups", resourceCount, groupCount);

                    // Directly pass the configurations to the ResourceRegistry
                    resourceRegistry.validateAndStoreResources(resourceConfigs);
                })
                .doOnError(error -> LoggingUtils.error("Failed to load resources: {}", error.getMessage()))
                .subscribe();
    }

    private Mono<List<ResourceConfig>> fetchConfig() {
        return webClient.get()
                .uri(serverProperties.getUrl())
                .headers(headers -> headers.setBasicAuth(serverProperties.getUsername(), serverProperties.getPassword()))
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .retryWhen(Retry.backoff(5, Duration.ofSeconds(30)))
                .map(this::extractResourceConfigs);
    }

    private List<ResourceConfig> extractResourceConfigs(String response) {
        try {
            JsonNode rootNode = objectMapper.readTree(response);
            JsonNode propertySources = validatePropertySources(rootNode);
            JsonNode sourceNode = validateSourceNode(propertySources);

            return parseResourceConfigs(sourceNode);

        } catch (Exception e) {
            throw new ConfigParsingException("Error parsing config response", e);
        }
    }

    private JsonNode validatePropertySources(JsonNode rootNode) {
        JsonNode propertySources = rootNode.path("propertySources");
        if (!propertySources.isArray() || propertySources.isEmpty()) {
            throw new ConfigParsingException("Invalid config response: Missing propertySources");
        }
        return propertySources;
    }

    private JsonNode validateSourceNode(JsonNode propertySources) {
        JsonNode sourceNode = propertySources.get(0).path("source");
        if (sourceNode.isMissingNode()) {
            throw new ConfigParsingException("Invalid config response: Missing source");
        }
        return sourceNode;
    }

    private List<ResourceConfig> parseResourceConfigs(JsonNode sourceNode) {
        Map<String, ResourceConfig> configMap = new HashMap<>();
        Pattern pattern = Pattern.compile("document\\[(\\d+)]\\.resources\\[(\\d+)]\\.(.+)");

        sourceNode.fields().forEachRemaining(entry -> processEntry(entry, pattern, sourceNode, configMap));

        return new ArrayList<>(configMap.values());
    }

    private void processEntry(Map.Entry<String, JsonNode> entry, Pattern pattern, JsonNode sourceNode, Map<String, ResourceConfig> configMap) {
        Matcher matcher = pattern.matcher(entry.getKey());
        if (!matcher.matches()) return;

        String groupIndex = matcher.group(1);
        int resourceIdx = Integer.parseInt(matcher.group(2));
        String property = matcher.group(3);
        String value = entry.getValue().asText();

        ResourceConfig resourceConfig = configMap.computeIfAbsent(groupIndex, k -> new ResourceConfig());
        resourceConfig.setGroup(sourceNode.path("document[" + groupIndex + "].group").asText());

        ensureResourceListSize(resourceConfig, resourceIdx);
        setResourceProperty(resourceConfig.getResources().get(resourceIdx), property, value);
    }

    private void ensureResourceListSize(ResourceConfig resourceConfig, int resourceIdx) {
        if (resourceConfig.getResources() == null) {
            resourceConfig.setResources(new ArrayList<>());
        }
        while (resourceConfig.getResources().size() <= resourceIdx) {
            resourceConfig.getResources().add(new Resource());
        }
    }

    private void setResourceProperty(Resource resource, String property, String value) {
        switch (property) {
            case "method" -> resource.setMethod(value);
            case "path" -> resource.setPath(value);
            case "endpoint" -> resource.setEndpoint(value);
            case "operation" -> resource.setOperation(value);
            case "microservice" -> resource.setMicroservice(value);
            case "authType" -> resource.setAuthType(value);
            case "basicAuthCredentials" -> resource.setBasicAuthCredentials(value);
            case "exemptMsisdnHeader" -> resource.setExemptMsisdnHeader(Boolean.parseBoolean(value));
            case "logActivity" -> resource.setLogActivity(Boolean.parseBoolean(value));
            case "permission" -> resource.setPermission(value);
            default -> LoggingUtils.warn("Unknown property encountered in config: {}", property);
        }
    }

    public static class ConfigParsingException extends RuntimeException {
        public ConfigParsingException(String message) {
            super(message);
        }

        public ConfigParsingException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}