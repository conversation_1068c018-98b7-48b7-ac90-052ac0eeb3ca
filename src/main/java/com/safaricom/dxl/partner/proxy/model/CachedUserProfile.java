package com.safaricom.dxl.partner.proxy.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a subset of user profile data that is cached.
 * This class contains only the fields that need to be cached.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CachedUserProfile {
    private String email;
    private String phone;
}
