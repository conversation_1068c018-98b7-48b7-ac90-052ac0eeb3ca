package com.safaricom.dxl.partner.proxy.service.impl;

import com.safaricom.dxl.partner.proxy.service.ProxyService;
import com.safaricom.dxl.partner.proxy.service.RequestPipeline;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * Implementation of the ProxyService that uses the request processing pipeline.
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProxyServiceImpl implements ProxyService {

    private final RequestPipeline requestPipeline;

    @Override
    public Mono<ResponseEntity<byte[]>> processRequest(ServerWebExchange exchange, Mono<byte[]> body) {
        return requestPipeline.process(exchange, body);
    }
}
