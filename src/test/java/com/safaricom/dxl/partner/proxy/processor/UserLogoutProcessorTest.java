package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.utils.MsUtilities;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.POST_USER_LOGOUT;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for UserLogoutProcessor.
 * Tests all methods with various scenarios including edge cases and error conditions.
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class UserLogoutProcessorTest {

    @Mock
    private CacheRepository cacheRepository;

    @Mock
    private WsStarterService starterProperties;

    @Mock
    private ResponseUtils responseUtils;

    @Mock
    private RequestContext requestContext;

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @Mock
    private Resource resource;

    private UserLogoutProcessor userLogoutProcessor;

    @BeforeEach
    void setUp() {
        userLogoutProcessor = new UserLogoutProcessor(cacheRepository, starterProperties, responseUtils);
    }

    @Nested
    @DisplayName("Initialization Tests")
    class InitializationTests {

        @Test
        @DisplayName("Should initialize processor successfully")
        void init_WhenCalled_LogsInitialization() {
            // When
            userLogoutProcessor.init();

            // Then - No exception should be thrown
            // Logging is tested through integration tests
            assertNotNull(userLogoutProcessor);
        }

        @Test
        @DisplayName("Should return correct default order")
        void getDefaultOrder_WhenCalled_ReturnsCorrectOrder() {
            // When
            int order = userLogoutProcessor.getDefaultOrder();

            // Then
            assertEquals(41, order);
        }
    }

    @Nested
    @DisplayName("shouldProcessIfEnabled() Tests")
    class ShouldProcessIfEnabledTests {

        @Test
        @DisplayName("Should process when operation is POST_USER_LOGOUT")
        void shouldProcessIfEnabled_WithLogoutOperation_ReturnsTrue() {
            // Given
            when(requestContext.getResource()).thenReturn(resource);
            when(resource.getOperation()).thenReturn(POST_USER_LOGOUT);

            // When
            boolean result = userLogoutProcessor.shouldProcessIfEnabled(requestContext);

            // Then
            assertTrue(result);
        }

        @Test
        @DisplayName("Should not process when operation is not POST_USER_LOGOUT")
        void shouldProcessIfEnabled_WithNonLogoutOperation_ReturnsFalse() {
            // Given
            when(requestContext.getResource()).thenReturn(resource);
            when(resource.getOperation()).thenReturn("GET_USERS");

            // When
            boolean result = userLogoutProcessor.shouldProcessIfEnabled(requestContext);

            // Then
            assertFalse(result);
        }

        @Test
        @DisplayName("Should not process when operation is null")
        void shouldProcessIfEnabled_WithNullOperation_ReturnsFalse() {
            // Given
            when(requestContext.getResource()).thenReturn(resource);
            when(resource.getOperation()).thenReturn(null);

            // When
            boolean result = userLogoutProcessor.shouldProcessIfEnabled(requestContext);

            // Then
            assertFalse(result);
        }
    }

    @Nested
    @DisplayName("process() Tests")
    class ProcessTests {

        private HttpHeaders headers;

        @BeforeEach
        void setUp() {
            headers = new HttpHeaders();
            when(requestContext.getExchange()).thenReturn(exchange);
            when(exchange.getRequest()).thenReturn(request);
            when(request.getHeaders()).thenReturn(headers);
        }

        @Test
        @DisplayName("Should successfully delete cache entries and return success response")
        void process_WithValidToken_DeletesCacheAndReturnsSuccess() {
            // Given
            String token = "test-token";
            String hashedToken = "hashed-token";
            String cacheKey = "90567ff8-6d28-36fa-919b-5dece8296c59"; // Mock UUID

            headers.set(X_IDENTITY, token);

            when(requestContext.getResource()).thenReturn(resource);
            when(starterProperties.hashText(token)).thenReturn(hashedToken);
            when(cacheRepository.delete("ProxyAuth-" + hashedToken)).thenReturn(Mono.just(true));
            when(cacheRepository.delete("phone" + cacheKey)).thenReturn(Mono.just(true));
            when(cacheRepository.delete(cacheKey)).thenReturn(Mono.just(true));

            ResponseEntity<byte[]> successResponse = ResponseEntity.ok().build();
            when(responseUtils.errorResponseReactive(eq(headers), eq(resource), eq(HttpStatus.OK), anyString(), isNull()))
                    .thenReturn(Mono.just(successResponse));

            // When & Then
            try (MockedStatic<MsUtilities> mockedUtilities = mockStatic(MsUtilities.class)) {
                mockedUtilities.when(() -> MsUtilities.generateId(token)).thenReturn(cacheKey);

                Mono<ProcessingResult> result = userLogoutProcessor.process(requestContext);

                StepVerifier.create(result)
                        .expectNextMatches(processingResult -> {
                            assertFalse(processingResult.isContinueProcessing());
                            assertEquals(successResponse, processingResult.getResponse());
                            return true;
                        })
                        .verifyComplete();

                verify(cacheRepository).delete("ProxyAuth-" + hashedToken);
                verify(cacheRepository).delete("phone" + cacheKey);
                verify(cacheRepository).delete(cacheKey);
                verify(responseUtils).errorResponseReactive(eq(headers), eq(resource), eq(HttpStatus.OK), anyString(), isNull());
            }
        }

        @Test
        @DisplayName("Should continue processing when no token found in headers")
        void process_WithNoToken_ContinuesProcessing() {
            // Given - no X_IDENTITY header set (headers is empty)

            // When
            Mono<ProcessingResult> result = userLogoutProcessor.process(requestContext);

            // Then
            StepVerifier.create(result)
                    .expectNextMatches(processingResult -> {
                        assertTrue(processingResult.isContinueProcessing());
                        assertNull(processingResult.getResponse());
                        return true;
                    })
                    .verifyComplete();

            verify(cacheRepository, never()).delete(anyString());
            verify(responseUtils, never()).errorResponseReactive(any(), any(), any(), any(), any());
        }





        @Test
        @DisplayName("Should handle empty token by processing it")
        void process_WithEmptyToken_ProcessesEmptyToken() {
            // Given
            String emptyToken = "";
            String hashedToken = "hashed-empty-token";
            String cacheKey = "empty-cache-key";

            headers.set(X_IDENTITY, emptyToken);

            when(requestContext.getResource()).thenReturn(resource);
            when(starterProperties.hashText(emptyToken)).thenReturn(hashedToken);
            when(cacheRepository.delete("ProxyAuth-" + hashedToken)).thenReturn(Mono.just(true));
            when(cacheRepository.delete("phone" + cacheKey)).thenReturn(Mono.just(true));
            when(cacheRepository.delete(cacheKey)).thenReturn(Mono.just(true));

            ResponseEntity<byte[]> successResponse = ResponseEntity.ok().build();
            when(responseUtils.errorResponseReactive(eq(headers), eq(resource), eq(HttpStatus.OK), anyString(), isNull()))
                    .thenReturn(Mono.just(successResponse));

            // When & Then
            try (MockedStatic<MsUtilities> mockedUtilities = mockStatic(MsUtilities.class)) {
                mockedUtilities.when(() -> MsUtilities.generateId(emptyToken)).thenReturn(cacheKey);

                Mono<ProcessingResult> result = userLogoutProcessor.process(requestContext);

                StepVerifier.create(result)
                        .expectNextMatches(processingResult -> {
                            assertFalse(processingResult.isContinueProcessing());
                            assertEquals(successResponse, processingResult.getResponse());
                            return true;
                        })
                        .verifyComplete();

                verify(cacheRepository).delete("ProxyAuth-" + hashedToken);
                verify(cacheRepository).delete("phone" + cacheKey);
                verify(cacheRepository).delete(cacheKey);
            }
        }

        @Test
        @DisplayName("Should handle response utils error")
        void process_WithResponseUtilsError_PropagatesError() {
            // Given
            String token = "test-token";
            String hashedToken = "hashed-token";
            String cacheKey = "90567ff8-6d28-36fa-919b-5dece8296c59";

            headers.set(X_IDENTITY, token);

            when(requestContext.getResource()).thenReturn(resource);
            when(starterProperties.hashText(token)).thenReturn(hashedToken);
            when(cacheRepository.delete("ProxyAuth-" + hashedToken)).thenReturn(Mono.just(true));
            when(cacheRepository.delete("phone" + cacheKey)).thenReturn(Mono.just(true));
            when(cacheRepository.delete(cacheKey)).thenReturn(Mono.just(true));
            when(responseUtils.errorResponseReactive(eq(headers), eq(resource), eq(HttpStatus.OK), anyString(), isNull()))
                    .thenReturn(Mono.error(new RuntimeException("Response utils error")));

            // When & Then
            try (MockedStatic<MsUtilities> mockedUtilities = mockStatic(MsUtilities.class)) {
                mockedUtilities.when(() -> MsUtilities.generateId(token)).thenReturn(cacheKey);

                Mono<ProcessingResult> result = userLogoutProcessor.process(requestContext);

                StepVerifier.create(result)
                        .expectError(RuntimeException.class)
                        .verify();

                verify(cacheRepository).delete("ProxyAuth-" + hashedToken);
                verify(cacheRepository).delete("phone" + cacheKey);
                verify(cacheRepository).delete(cacheKey);
            }
        }
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("Should create instance with valid dependencies")
        void constructor_WithValidDependencies_CreatesInstance() {
            // When
            UserLogoutProcessor processor = new UserLogoutProcessor(cacheRepository, starterProperties, responseUtils);

            // Then
            assertNotNull(processor);
        }
    }
}
