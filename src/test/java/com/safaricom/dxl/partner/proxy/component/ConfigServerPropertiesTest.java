package com.safaricom.dxl.partner.proxy.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for ConfigServerProperties.
 * Tests all methods with various scenarios including edge cases and error conditions.
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ConfigServerPropertiesTest {

    @Mock
    private Environment environment;

    private ConfigServerProperties configServerProperties;

    @BeforeEach
    void setUp() {
        configServerProperties = new ConfigServerProperties(environment);
    }

    @Nested
    @DisplayName("getUrl() Tests")
    class GetUrlTests {

        @Test
        @DisplayName("Should construct URL with environment variable profile and config import")
        void getUrl_WithEnvironmentVariables_ReturnsCorrectUrl() {
            // Given
            when(environment.getProperty("SPRING_PROFILES_ACTIVE")).thenReturn("production");
            when(environment.getProperty("spring.profiles.active")).thenReturn("fallback-profile");
            when(environment.getProperty("SPRING_CONFIG_IMPORT")).thenReturn("optional:configserver:http://config-server:8888");
            when(environment.getProperty("spring.config.import")).thenReturn("fallback-config");

            // When
            String result = configServerProperties.getUrl();

            // Then
            assertEquals("http://config-server:8888/partner-portal-proxy-resources/production", result);
            verify(environment).getProperty("SPRING_PROFILES_ACTIVE");
            verify(environment).getProperty("SPRING_CONFIG_IMPORT");
        }

        @Test
        @DisplayName("Should construct URL with property-based profile and config import")
        void getUrl_WithPropertyBasedValues_ReturnsCorrectUrl() {
            // Given
            when(environment.getProperty("SPRING_PROFILES_ACTIVE")).thenReturn(null);
            when(environment.getProperty("spring.profiles.active")).thenReturn("development");
            when(environment.getProperty("SPRING_CONFIG_IMPORT")).thenReturn(null);
            when(environment.getProperty("spring.config.import")).thenReturn("configserver:http://localhost:8888");

            // When
            String result = configServerProperties.getUrl();

            // Then
            assertEquals("http://localhost:8888/partner-portal-proxy-resources/development", result);
            verify(environment).getProperty("SPRING_PROFILES_ACTIVE");
            verify(environment).getProperty("spring.profiles.active");
            verify(environment).getProperty("SPRING_CONFIG_IMPORT");
            verify(environment).getProperty("spring.config.import");
        }

        @ParameterizedTest
        @CsvSource({
            "test, optional:configserver:http://test-server:8888, http://test-server:8888/partner-portal-proxy-resources/test, Should handle config import with optional prefix",
            "staging, configserver:http://staging-server:8888, http://staging-server:8888/partner-portal-proxy-resources/staging, Should handle config import with configserver prefix",
            "integration, optional:configserver:http://integration-server:8888, http://integration-server:8888/partner-portal-proxy-resources/integration, Should handle config import with both prefixes",
            "local, '  optional:configserver:http://local-server:8888  ', http://local-server:8888/partner-portal-proxy-resources/local, Should trim whitespace from config import"
        })
        @DisplayName("Should handle various config import prefix scenarios")
        void getUrl_WithVariousPrefixes_RemovesPrefixesAndTrimsWhitespace(String profile, String configImport, String expectedUrl, String description) {
            // Given
            when(environment.getProperty("SPRING_PROFILES_ACTIVE")).thenReturn(profile);
            when(environment.getProperty("spring.profiles.active")).thenReturn("fallback-profile");
            when(environment.getProperty("SPRING_CONFIG_IMPORT")).thenReturn(configImport);
            when(environment.getProperty("spring.config.import")).thenReturn("fallback-config");

            // When
            String result = configServerProperties.getUrl();

            // Then
            assertEquals(expectedUrl, result);
        }

        @Test
        @DisplayName("Should throw NullPointerException when active profile is null")
        void getUrl_WithNullActiveProfile_ThrowsNullPointerException() {
            // Given
            when(environment.getProperty("SPRING_PROFILES_ACTIVE")).thenReturn(null);
            when(environment.getProperty("spring.profiles.active")).thenReturn(null);

            // When & Then
            assertThrows(NullPointerException.class, () -> configServerProperties.getUrl());
            verify(environment).getProperty("SPRING_PROFILES_ACTIVE");
            verify(environment).getProperty("spring.profiles.active");
        }

        @Test
        @DisplayName("Should throw NullPointerException when config import is null")
        void getUrl_WithNullConfigImport_ThrowsNullPointerException() {
            // Given
            when(environment.getProperty("SPRING_PROFILES_ACTIVE")).thenReturn("test");
            when(environment.getProperty("spring.profiles.active")).thenReturn("fallback-profile");
            when(environment.getProperty("SPRING_CONFIG_IMPORT")).thenReturn(null);
            when(environment.getProperty("spring.config.import")).thenReturn(null);

            // When & Then
            assertThrows(NullPointerException.class, () -> configServerProperties.getUrl());
            verify(environment).getProperty("SPRING_PROFILES_ACTIVE");
            verify(environment).getProperty("SPRING_CONFIG_IMPORT");
            verify(environment).getProperty("spring.config.import");
        }
    }

    @Nested
    @DisplayName("getUsername() Tests")
    class GetUsernameTests {

        @Test
        @DisplayName("Should return username from environment variable")
        void getUsername_WithEnvironmentVariable_ReturnsUsername() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_USERNAME")).thenReturn("admin");

            // When
            String result = configServerProperties.getUsername();

            // Then
            assertEquals("admin", result);
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_USERNAME");
        }

        @Test
        @DisplayName("Should return username from property when environment variable is null")
        void getUsername_WithProperty_ReturnsUsername() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_USERNAME")).thenReturn(null);
            when(environment.getProperty("spring.cloud.config.username")).thenReturn("config-user");

            // When
            String result = configServerProperties.getUsername();

            // Then
            assertEquals("config-user", result);
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_USERNAME");
            verify(environment).getProperty("spring.cloud.config.username");
        }

        @Test
        @DisplayName("Should throw NullPointerException when username is null")
        void getUsername_WithNullUsername_ThrowsNullPointerException() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_USERNAME")).thenReturn(null);
            when(environment.getProperty("spring.cloud.config.username")).thenReturn(null);

            // When & Then
            assertThrows(NullPointerException.class, () -> configServerProperties.getUsername());
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_USERNAME");
            verify(environment).getProperty("spring.cloud.config.username");
        }

        @Test
        @DisplayName("Should prefer environment variable over property")
        void getUsername_WithBothValues_PrefersEnvironmentVariable() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_USERNAME")).thenReturn("env-user");
            // Note: The Optional.ofNullable().orElse() pattern will still call the second property
            // even if the first returns a non-null value, but it won't use the result
            when(environment.getProperty("spring.cloud.config.username")).thenReturn("prop-user");

            // When
            String result = configServerProperties.getUsername();

            // Then
            assertEquals("env-user", result);
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_USERNAME");
            verify(environment).getProperty("spring.cloud.config.username");
        }
    }

    @Nested
    @DisplayName("getPassword() Tests")
    class GetPasswordTests {

        @Test
        @DisplayName("Should return password from environment variable")
        void getPassword_WithEnvironmentVariable_ReturnsPassword() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_PASSWORD")).thenReturn("secret123");

            // When
            String result = configServerProperties.getPassword();

            // Then
            assertEquals("secret123", result);
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_PASSWORD");
        }

        @Test
        @DisplayName("Should return password from property when environment variable is null")
        void getPassword_WithProperty_ReturnsPassword() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_PASSWORD")).thenReturn(null);
            when(environment.getProperty("spring.cloud.config.password")).thenReturn("config-pass");

            // When
            String result = configServerProperties.getPassword();

            // Then
            assertEquals("config-pass", result);
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_PASSWORD");
            verify(environment).getProperty("spring.cloud.config.password");
        }

        @Test
        @DisplayName("Should throw NullPointerException when password is null")
        void getPassword_WithNullPassword_ThrowsNullPointerException() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_PASSWORD")).thenReturn(null);
            when(environment.getProperty("spring.cloud.config.password")).thenReturn(null);

            // When & Then
            assertThrows(NullPointerException.class, () -> configServerProperties.getPassword());
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_PASSWORD");
            verify(environment).getProperty("spring.cloud.config.password");
        }

        @Test
        @DisplayName("Should prefer environment variable over property")
        void getPassword_WithBothValues_PrefersEnvironmentVariable() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_PASSWORD")).thenReturn("env-pass");
            // Note: The Optional.ofNullable().orElse() pattern will still call the second property
            // even if the first returns a non-null value, but it won't use the result
            when(environment.getProperty("spring.cloud.config.password")).thenReturn("prop-pass");

            // When
            String result = configServerProperties.getPassword();

            // Then
            assertEquals("env-pass", result);
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_PASSWORD");
            verify(environment).getProperty("spring.cloud.config.password");
        }

        @Test
        @DisplayName("Should handle empty string password")
        void getPassword_WithEmptyString_ReturnsEmptyString() {
            // Given
            when(environment.getProperty("SPRING_CLOUD_CONFIG_PASSWORD")).thenReturn("");

            // When
            String result = configServerProperties.getPassword();

            // Then
            assertEquals("", result);
            verify(environment).getProperty("SPRING_CLOUD_CONFIG_PASSWORD");
        }
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("Should create instance with valid environment")
        void constructor_WithValidEnvironment_CreatesInstance() {
            // Given
            Environment testEnvironment = mock(Environment.class);

            // When
            ConfigServerProperties instance = new ConfigServerProperties(testEnvironment);

            // Then
            assertNotNull(instance);
        }

        @Test
        @DisplayName("Should accept null environment (Lombok constructor)")
        void constructor_WithNullEnvironment_AcceptsNull() {
            // When
            ConfigServerProperties instance = new ConfigServerProperties(null);

            // Then
            assertNotNull(instance);
            // Note: Lombok's @RequiredArgsConstructor doesn't validate null parameters
            // The NullPointerException will occur when methods are called, not during construction
        }

        @Test
        @DisplayName("Should throw NPE when calling methods with null environment")
        void methods_WithNullEnvironment_ThrowNullPointerException() {
            // Given
            ConfigServerProperties instance = new ConfigServerProperties(null);

            // When & Then
            assertThrows(NullPointerException.class, instance::getUrl);
            assertThrows(NullPointerException.class, instance::getUsername);
            assertThrows(NullPointerException.class, instance::getPassword);
        }
    }
}
