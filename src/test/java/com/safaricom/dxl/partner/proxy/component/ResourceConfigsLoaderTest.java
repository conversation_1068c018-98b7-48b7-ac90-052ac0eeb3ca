package com.safaricom.dxl.partner.proxy.component;

import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.ResourceConfig;
import com.safaricom.dxl.partner.proxy.service.ResourceRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ResourceConfigsLoaderTest {

    @Mock
    private ConfigServerProperties serverProperties;

    @Mock
    private ResourceRegistry resourceRegistry;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Mock
    private ExchangeFunction exchangeFunction;

    private WebClient webClient;
    private ResourceConfigsLoader resourceConfigsLoader;

    @BeforeEach
    void setUp() {
        webClient = WebClient.builder()
                .exchangeFunction(exchangeFunction)
                .build();

        when(serverProperties.getUrl()).thenReturn("http://config-server/config");
        when(serverProperties.getUsername()).thenReturn("user");
        when(serverProperties.getPassword()).thenReturn("password");

        resourceConfigsLoader = new ResourceConfigsLoader(
                serverProperties,
                webClient,
                resourceRegistry,
                eventPublisher
        );
    }

    @Test
    @DisplayName("Should successfully load and process resource configurations")
    void init_SuccessfulConfigLoad_ProcessesConfigurations() {
        // Arrange
        String validResponse = """
                {
                  "propertySources": [
                    {
                      "name": "config-server",
                      "source": {
                        "document[0].group": "api",
                        "document[0].resources[0].method": "GET",
                        "document[0].resources[0].path": "/users",
                        "document[0].resources[0].endpoint": "/api/users",
                        "document[0].resources[0].operation": "GET_USERS",
                        "document[0].resources[0].microservice": "user-service",
                        "document[0].resources[0].authType": "Bearer",
                        "document[0].resources[0].exemptMsisdnHeader": "false",
                        "document[0].resources[0].logActivity": "true",
                        "document[0].resources[0].permission": "READ_USERS",
                        "document[0].resources[1].method": "POST",
                        "document[0].resources[1].path": "/users",
                        "document[0].resources[1].endpoint": "/api/users",
                        "document[0].resources[1].operation": "CREATE_USER",
                        "document[0].resources[1].microservice": "user-service",
                        "document[0].resources[1].basicAuthCredentials": "user:pass",
                        "document[1].group": "admin",
                        "document[1].resources[0].method": "GET",
                        "document[1].resources[0].path": "/admin/users",
                        "document[1].resources[0].endpoint": "/api/admin/users",
                        "document[1].resources[0].operation": "ADMIN_GET_USERS",
                        "document[1].resources[0].microservice": "admin-service"
                      }
                    }
                  ]
                }
                """;

        mockWebClientResponse(validResponse, HttpStatus.OK);

        // Act
        resourceConfigsLoader.init();

        // Assert
        ArgumentCaptor<List<ResourceConfig>> configCaptor = ArgumentCaptor.forClass(List.class);
        verify(resourceRegistry).validateAndStoreResources(configCaptor.capture());

        List<ResourceConfig> capturedConfigs = configCaptor.getValue();
        assertEquals(2, capturedConfigs.size());

        // Verify first group
        ResourceConfig firstGroup = capturedConfigs.get(0);
        assertEquals("api", firstGroup.getGroup());
        assertEquals(2, firstGroup.getResources().size());

        // Verify first resource in first group
        Resource firstResource = firstGroup.getResources().get(0);
        assertEquals("GET", firstResource.getMethod());
        assertEquals("/users", firstResource.getPath());
        assertEquals("/api/users", firstResource.getEndpoint());
        assertEquals("GET_USERS", firstResource.getOperation());
        assertEquals("user-service", firstResource.getMicroservice());
        assertEquals("Bearer", firstResource.getAuthType());
        assertFalse(firstResource.getExemptMsisdnHeader());
        assertTrue(firstResource.getLogActivity());
        assertEquals("READ_USERS", firstResource.getPermission());

        // Verify second resource in first group
        Resource secondResource = firstGroup.getResources().get(1);
        assertEquals("POST", secondResource.getMethod());
        assertEquals("CREATE_USER", secondResource.getOperation());
        assertEquals("user:pass", secondResource.getBasicAuthCredentials());

        // Verify second group
        ResourceConfig secondGroup = capturedConfigs.get(1);
        assertEquals("admin", secondGroup.getGroup());
        assertEquals(1, secondGroup.getResources().size());

        // Verify first resource in second group
        Resource adminResource = secondGroup.getResources().get(0);
        assertEquals("GET", adminResource.getMethod());
        assertEquals("/admin/users", adminResource.getPath());
        assertEquals("ADMIN_GET_USERS", adminResource.getOperation());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("invalidConfigResponses")
    @DisplayName("Should handle invalid config responses")
    void extractResourceConfigs_InvalidResponses_HandlesGracefully(String testName, String invalidResponse) {
        // Arrange
        mockWebClientResponse(invalidResponse, HttpStatus.OK);

        // Act
        resourceConfigsLoader.init();

        // Assert
        verify(resourceRegistry, never()).validateAndStoreResources(anyList());
    }

    static Stream<Arguments> invalidConfigResponses() {
        return Stream.of(
            Arguments.of(
                "Missing property sources",
                """
                {
                  "name": "config-server",
                  "source": {}
                }
                """
            ),
            Arguments.of(
                "Empty property sources",
                """
                {
                  "propertySources": []
                }
                """
            ),
            Arguments.of(
                "Missing source in property sources",
                """
                {
                  "propertySources": [
                    {
                      "name": "config-server"
                    }
                  ]
                }
                """
            )
        );
    }

    @Test
    @DisplayName("Should handle invalid JSON in config response")
    void extractResourceConfigs_InvalidJson_ThrowsException() {
        // Arrange
        String invalidResponse = "invalid json";

        mockWebClientResponse(invalidResponse, HttpStatus.OK);

        // Act & Assert
        resourceConfigsLoader.init();

        // Verify error is logged
        verify(resourceRegistry, never()).validateAndStoreResources(anyList());
    }

    @Test
    @DisplayName("Should handle server error response")
    void fetchConfig_ServerError_ReturnsEmptyMono() {
        // Arrange
        mockWebClientResponse("Server Error", HttpStatus.INTERNAL_SERVER_ERROR);

        // Act & Assert
        resourceConfigsLoader.init();

        // Verify error is logged
        verify(resourceRegistry, never()).validateAndStoreResources(anyList());
    }

    @Test
    @DisplayName("Should handle unknown resource property")
    void setResourceProperty_UnknownProperty_LogsWarning() {
        // Arrange
        String response = """
                {
                  "propertySources": [
                    {
                      "name": "config-server",
                      "source": {
                        "document[0].group": "api",
                        "document[0].resources[0].method": "GET",
                        "document[0].resources[0].unknownProperty": "value"
                      }
                    }
                  ]
                }
                """;

        mockWebClientResponse(response, HttpStatus.OK);

        // Act
        resourceConfigsLoader.init();

        // Assert
        ArgumentCaptor<List<ResourceConfig>> configCaptor = ArgumentCaptor.forClass(List.class);
        verify(resourceRegistry).validateAndStoreResources(configCaptor.capture());

        List<ResourceConfig> capturedConfigs = configCaptor.getValue();
        assertEquals(1, capturedConfigs.size());
        assertEquals("api", capturedConfigs.get(0).getGroup());
        assertEquals(1, capturedConfigs.get(0).getResources().size());
        assertEquals("GET", capturedConfigs.get(0).getResources().get(0).getMethod());
    }

    @Test
    @DisplayName("Should handle non-resource properties")
    void processEntry_NonResourceProperty_IgnoresProperty() {
        // Arrange
        String response = """
                {
                  "propertySources": [
                    {
                      "name": "config-server",
                      "source": {
                        "document[0].group": "api",
                        "document[0].resources[0].method": "GET",
                        "some.other.property": "value"
                      }
                    }
                  ]
                }
                """;

        mockWebClientResponse(response, HttpStatus.OK);

        // Act
        resourceConfigsLoader.init();

        // Assert
        ArgumentCaptor<List<ResourceConfig>> configCaptor = ArgumentCaptor.forClass(List.class);
        verify(resourceRegistry).validateAndStoreResources(configCaptor.capture());

        List<ResourceConfig> capturedConfigs = configCaptor.getValue();
        assertEquals(1, capturedConfigs.size());
        assertEquals("api", capturedConfigs.get(0).getGroup());
        assertEquals(1, capturedConfigs.get(0).getResources().size());
        assertEquals("GET", capturedConfigs.get(0).getResources().get(0).getMethod());
    }

    private void mockWebClientResponse(String responseBody, HttpStatus status) {
        ClientResponse clientResponse = ClientResponse.create(status)
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .body(responseBody)
                .build();

        when(exchangeFunction.exchange(any())).thenReturn(Mono.just(clientResponse));
    }
}
