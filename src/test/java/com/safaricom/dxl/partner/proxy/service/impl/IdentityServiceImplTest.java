package com.safaricom.dxl.partner.proxy.service.impl;

import com.safaricom.dxl.partner.proxy.config.BasicAuthConfig;
import com.safaricom.dxl.partner.proxy.config.UserProfileConfig;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.UserInfoResponse;
import com.safaricom.dxl.partner.proxy.model.UserProfile;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.service.ResourceRegistry;
import com.safaricom.dxl.webflux.starter.model.WsHeader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_SOURCE_SYSTEM;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class IdentityServiceImplTest {

    @Mock
    private WebClient webClient;

    @Mock
    private CacheRepository cacheRepository;

    @Mock
    private ResourceRegistry resourceRegistry;

    @Mock
    private BasicAuthConfig basicAuthConfig;

    @Mock
    private UserProfileConfig userProfileConfig;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @InjectMocks
    private IdentityServiceImpl identityService;

    private HttpHeaders headers;
    private UserProfile userProfile;
    private UserInfoResponse userInfoResponse;
    private Resource identityResource;

    @BeforeEach
    void setUp() {
        // Set up headers
        headers = new HttpHeaders();
        headers.add(X_IDENTITY, "testuser");

        // Set up user profile
        userProfile = new UserProfile();
        userProfile.setUsername("testuser");
        userProfile.setSub("test-sub");
        userProfile.setEmail("<EMAIL>");
        userProfile.setPhoneNumber("254712345678");

        // Set up user info response
        userInfoResponse = new UserInfoResponse();
        WsHeader header = new WsHeader();
        header.setResponseCode(200);
        userInfoResponse.setHeader(header);
        userInfoResponse.setBody(userProfile);

        // Set up WebClient mock chain
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(UserInfoResponse.class)).thenReturn(Mono.just(userInfoResponse));

        // Set up identity resource
        identityResource = new Resource();
        identityResource.setOperation(GET_LOGGED_IN_USER_PROFILE);
        identityResource.setEndpoint("http://identity-service/api/users/profile");
        identityResource.setAuthType("basic");
        identityResource.setBasicAuthCredentials("identity-creds");

        List<Resource> resources = new ArrayList<>();
        resources.add(identityResource);
        when(resourceRegistry.getResources()).thenReturn(resources);

        // Set up basic auth config
        when(basicAuthConfig.getCredentials()).thenReturn(Collections.singletonMap("identity-creds", "username:password"));

        // Set up user profile config
        when(userProfileConfig.getCachePrefix()).thenReturn("user:");
        when(userProfileConfig.getCacheDuration()).thenReturn("PT1H");

        // Set up cache repository
        when(cacheRepository.setData(anyString(), any(), any(Duration.class))).thenReturn(Mono.just(true));
    }

    @Test
    @DisplayName("Should get user info from identity service")
    void getUserInfo_ValidRequest_ReturnsUserInfo() {
        // Act
        Mono<UserInfoResponse> result = identityService.getUserInfo(headers);

        // Assert
        StepVerifier.create(result)
            .assertNext(response -> {
                assertEquals(200, response.getHeader().getResponseCode());
                // Don't check username as it's extracted from email in the service
                assertEquals("test-sub", response.getBody().getSub());
                assertEquals("<EMAIL>", response.getBody().getEmail());
                assertEquals("254712345678", response.getBody().getPhoneNumber());
            })
            .verifyComplete();

        // Verify WebClient interactions
        verify(webClient).get();
        verify(requestHeadersUriSpec).uri("http://identity-service/api/users/profile");
        verify(requestHeadersSpec).headers(any());
        verify(requestHeadersSpec).retrieve();
        verify(responseSpec).bodyToMono(UserInfoResponse.class);

        // Verify cache interaction
        verify(cacheRepository).setData(eq("user:testuser"), any(UserProfile.class), any(Duration.class));
    }

    @Test
    @DisplayName("Should set correct headers when calling identity service")
    void getUserInfo_HeadersSetCorrectly() {
        // Capture the headers consumer
        ArgumentCaptor<Consumer<HttpHeaders>> headersCaptor = ArgumentCaptor.forClass(Consumer.class);

        // Act
        identityService.getUserInfo(headers).subscribe();

        // Verify and capture headers
        verify(requestHeadersSpec).headers(headersCaptor.capture());

        // Apply the captured consumer to a new headers object
        HttpHeaders capturedHeaders = new HttpHeaders();
        headersCaptor.getValue().accept(capturedHeaders);

        // Verify headers were set correctly
        assertEquals("identity", capturedHeaders.getFirst(X_SOURCE_SYSTEM));
    }

    @Test
    @DisplayName("Should handle non-200 response from identity service")
    void getUserInfo_NonSuccessResponse_ReturnsError() {
        // Arrange
        UserInfoResponse errorResponse = new UserInfoResponse();
        WsHeader errorHeader = new WsHeader();
        errorHeader.setResponseCode(400);
        errorResponse.setHeader(errorHeader);

        when(responseSpec.bodyToMono(UserInfoResponse.class)).thenReturn(Mono.just(errorResponse));

        // Act
        Mono<UserInfoResponse> result = identityService.getUserInfo(headers);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable.getMessage().contains(GET_LOGGED_IN_USER_PROFILE) &&
                throwable.getMessage().contains("returned failure response"))
            .verify();
    }

    @Test
    @DisplayName("Should handle missing identity resource")
    void getUserInfo_MissingResource_ReturnsError() {
        // Set up empty resources
        when(resourceRegistry.getResources()).thenReturn(new ArrayList<>());

        // Act
        Mono<UserInfoResponse> result = identityService.getUserInfo(headers);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable.getMessage().contains(GET_LOGGED_IN_USER_PROFILE) &&
                throwable.getMessage().contains("resource not configured")
            )
            .verify();
    }

    @Test
    @DisplayName("Should get user profile from cache if available")
    void getUserProfile_CacheHit_ReturnsCachedProfile() {
        // Set up cache hit
        when(cacheRepository.getData("user:testuser", UserProfile.class))
                .thenReturn(Mono.just(userProfile));

        // Act
        Mono<UserProfile> result = identityService.getUserProfile(headers);

        // Assert
        StepVerifier.create(result)
            .assertNext(profile -> {
                assertEquals("testuser", profile.getUsername());
                assertEquals("test-sub", profile.getSub());
                assertEquals("<EMAIL>", profile.getEmail());
                assertEquals("254712345678", profile.getPhoneNumber());
            })
            .verifyComplete();

        // Verify cache was checked
        verify(cacheRepository).getData("user:testuser", UserProfile.class);
    }

    @Test
    @DisplayName("Should get user profile from identity service if not in cache")
    void getUserProfile_CacheMiss_GetsFromIdentityService() {
        // Set up cache miss
        when(cacheRepository.getData("user:testuser", UserProfile.class))
                .thenReturn(Mono.empty());

        // Act
        Mono<UserProfile> result = identityService.getUserProfile(headers);

        // Assert
        StepVerifier.create(result)
            .assertNext(profile -> {
                // Don't check username as it's extracted from email in the service
                assertEquals("test-sub", profile.getSub());
                assertEquals("<EMAIL>", profile.getEmail());
                assertEquals("254712345678", profile.getPhoneNumber());
            })
            .verifyComplete();

        // Verify cache was checked
        verify(cacheRepository).getData("user:testuser", UserProfile.class);

        // Verify identity service was called
        verify(webClient).get();

        // Verify result was cached
        verify(cacheRepository).setData(eq("user:testuser"), any(UserProfile.class), any(Duration.class));
    }

    @Test
    @DisplayName("Should extract username from email")
    void getUserInfo_ExtractsUsernameFromEmail() {
        // Set up user profile with email but no username
        UserProfile profileWithoutUsername = new UserProfile();
        profileWithoutUsername.setEmail("<EMAIL>");
        profileWithoutUsername.setSub("test-sub");

        UserInfoResponse response = new UserInfoResponse();
        WsHeader header = new WsHeader();
        header.setResponseCode(200);
        response.setHeader(header);
        response.setBody(profileWithoutUsername);

        when(responseSpec.bodyToMono(UserInfoResponse.class)).thenReturn(Mono.just(response));

        // Act
        Mono<UserInfoResponse> result = identityService.getUserInfo(headers);

        // Assert
        StepVerifier.create(result)
            .assertNext(userInfoResp -> {
                assertEquals("john.doe", userInfoResp.getBody().getUsername());
                assertEquals("<EMAIL>", userInfoResp.getBody().getEmail());
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("Should handle non-basic auth type")
    void getUserInfo_NonBasicAuthType_DoesNotSetBasicAuth() {
        // Arrange
        Resource nonBasicResource = new Resource();
        nonBasicResource.setOperation(GET_LOGGED_IN_USER_PROFILE);
        nonBasicResource.setEndpoint("http://identity-service/api/users/profile");
        nonBasicResource.setAuthType("oauth"); // Non-basic auth type

        List<Resource> resources = new ArrayList<>();
        resources.add(nonBasicResource);
        when(resourceRegistry.getResources()).thenReturn(resources);

        // Capture the headers consumer
        ArgumentCaptor<Consumer<HttpHeaders>> headersCaptor = ArgumentCaptor.forClass(Consumer.class);

        // Act
        identityService.getUserInfo(headers).subscribe();

        // Verify and capture headers
        verify(requestHeadersSpec).headers(headersCaptor.capture());

        // Apply the captured consumer to a new headers object
        HttpHeaders capturedHeaders = new HttpHeaders();
        headersCaptor.getValue().accept(capturedHeaders);

        // Verify headers were set correctly
        assertEquals("identity", capturedHeaders.getFirst(X_SOURCE_SYSTEM));
        // No need to verify basicAuthConfig.getCredentials() as it's called in setUp()
    }

    @Test
    @DisplayName("Should set basic auth when auth type is basic")
    void getUserInfo_BasicAuthType_SetsBasicAuth() {
        // Arrange - use the default setup which has basic auth type

        // Capture the headers consumer
        ArgumentCaptor<Consumer<HttpHeaders>> headersCaptor = ArgumentCaptor.forClass(Consumer.class);

        // Act
        identityService.getUserInfo(headers).subscribe();

        // Verify and capture headers
        verify(requestHeadersSpec).headers(headersCaptor.capture());

        // Apply the captured consumer to a new headers object
        HttpHeaders capturedHeaders = new HttpHeaders();
        headersCaptor.getValue().accept(capturedHeaders);

        // Verify headers were set correctly
        assertEquals("identity", capturedHeaders.getFirst(X_SOURCE_SYSTEM));
        // Verify that basic auth credentials were accessed (indicating basic auth was set)
        verify(basicAuthConfig).getCredentials();
    }

    @Test
    @DisplayName("Should handle getUserProfile when getUserInfo returns error")
    void getUserProfile_GetUserInfoError_ReturnsError() {
        // Arrange
        when(cacheRepository.getData("user:testuser", UserProfile.class)).thenReturn(Mono.empty());
        when(resourceRegistry.getResources()).thenReturn(new ArrayList<>()); // Empty resources to trigger error

        // Act
        Mono<UserProfile> result = identityService.getUserProfile(headers);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable.getMessage().contains(GET_LOGGED_IN_USER_PROFILE) &&
                throwable.getMessage().contains("resource not configured"))
            .verify();
    }

    @Test
    @DisplayName("Should handle getUserProfile when response code is not 200")
    void getUserProfile_NonSuccessResponseCode_ReturnsError() {
        // Arrange
        when(cacheRepository.getData(anyString(), eq(UserProfile.class))).thenReturn(Mono.empty());

        // Create a response with non-200 response code
        UserInfoResponse errorResponse = new UserInfoResponse();
        WsHeader errorHeader = new WsHeader();
        errorHeader.setResponseCode(400); // Non-200 response code
        errorResponse.setHeader(errorHeader);

        // Mock the resource registry and web client
        Resource resource = new Resource();
        resource.setOperation(GET_LOGGED_IN_USER_PROFILE);
        resource.setEndpoint("http://test-endpoint");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("test-credentials");

        when(resourceRegistry.getResources()).thenReturn(Collections.singletonList(resource));
        when(basicAuthConfig.getCredentials()).thenReturn(Collections.singletonMap("test-credentials", "test:test"));
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(UserInfoResponse.class)).thenReturn(Mono.just(errorResponse));

        // Act
        Mono<UserProfile> result = identityService.getUserProfile(headers);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(throwable ->
                throwable.getMessage().contains(GET_LOGGED_IN_USER_PROFILE) &&
                throwable.getMessage().contains("returned failure response"))
            .verify();
    }

    @Test
    @DisplayName("Should handle getUserProfile when response has null header")
    void getUserProfile_NullHeader_ThrowsNullPointerException() {
        // Arrange
        when(cacheRepository.getData(anyString(), eq(UserProfile.class))).thenReturn(Mono.empty());

        // Create a response with null header
        UserInfoResponse errorResponse = new UserInfoResponse();
        // No header set, so it will be null

        // Mock the resource registry and web client
        Resource resource = new Resource();
        resource.setOperation(GET_LOGGED_IN_USER_PROFILE);
        resource.setEndpoint("http://test-endpoint");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("test-credentials");

        when(resourceRegistry.getResources()).thenReturn(Collections.singletonList(resource));
        when(basicAuthConfig.getCredentials()).thenReturn(Collections.singletonMap("test-credentials", "test:test"));
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(UserInfoResponse.class)).thenReturn(Mono.just(errorResponse));

        // Act
        Mono<UserProfile> result = identityService.getUserProfile(headers);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(throwable -> throwable instanceof NullPointerException)
            .verify();
    }
}
