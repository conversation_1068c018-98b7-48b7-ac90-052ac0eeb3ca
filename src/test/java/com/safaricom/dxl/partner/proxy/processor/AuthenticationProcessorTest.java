
package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.*;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import com.safaricom.dxl.partner.proxy.service.IdentityService;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.POST_PARTNER_HUB_USER_REGISTRATION;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AuthenticationProcessorTest {

    @Mock
    private IdentityService identityService;
    
    @Mock
    private ResponseUtils responseUtils;
    
    @Mock
    private ServerWebExchange exchange;
    
    @Mock
    private ServerHttpRequest request;
    
    @Mock
    private CacheRepository cacheRepository;

    @Mock
    private WsStarterService starterProperties;

    @InjectMocks
    private AuthenticationProcessor processor;
    
    private RequestContext context;
    private Resource resource;
    private HttpHeaders headers;
    
    @BeforeEach
    void setUp() {
        // Set up headers
        headers = new HttpHeaders();
        
        // Set up request
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);
        
        // Set up resource
        resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/users");
        resource.setEndpoint("http://user-service/api/users");
        resource.setOperation("GET_USERS");
        
        // Set up context
        context = new RequestContext(exchange, Mono.empty());
        context.setResource(resource);
    }
    
    @Test
    @DisplayName("Should authenticate user and continue processing")
    void process_ValidUser_AuthenticatesAndContinues() {
        // Arrange
        UserProfile userProfile = new UserProfile();
        userProfile.setUsername("testuser");

        when(identityService.getUserProfile(any(HttpHeaders.class))).thenReturn(Mono.just(userProfile));
        when(starterProperties.hashText(any())).thenReturn("hashed-identity");
        when(cacheRepository.getData(anyString(), eq(SessionDetails.class))).thenReturn(Mono.just(new SessionDetails()));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertTrue(processingResult.isContinueProcessing());
                assertEquals(userProfile, context.getUserProfile());
            })
            .verifyComplete();

        verify(identityService).getUserProfile(any(HttpHeaders.class));
    }
    
    @Test
    @DisplayName("Should handle authentication failure")
    void process_AuthenticationFails_ReturnsErrorResponse() {
        // Arrange
        when(identityService.getUserProfile(any(HttpHeaders.class))).thenReturn(Mono.error(new RuntimeException("Authentication failed")));
        
        ResponseEntity<byte[]> errorResponse = ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        when(responseUtils.errorResponseReactive(
                any(HttpHeaders.class),
                eq(resource),
                eq(HttpStatus.UNAUTHORIZED),
                eq("GW_ERR7"),
                anyString()
        )).thenReturn(Mono.just(errorResponse));
        
        // Act
        Mono<ProcessingResult> result = processor.process(context);
        
        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertFalse(processingResult.isContinueProcessing());
                assertEquals(errorResponse, processingResult.getResponse());
            })
            .verifyComplete();
        
        verify(identityService).getUserProfile(any(HttpHeaders.class));
        verify(responseUtils).errorResponseReactive(
                any(HttpHeaders.class),
                eq(resource),
                eq(HttpStatus.UNAUTHORIZED),
                eq("GW_ERR7"),
                anyString()
        );
    }
    
    @ParameterizedTest
    @ValueSource(strings = {GET_LOGGED_IN_USER_PROFILE, POST_PARTNER_HUB_USER_REGISTRATION})
    @DisplayName("Should skip processing for excluded operations")
    void shouldProcessIfEnabled_ExcludedOperations_ReturnsFalse(String excludedOperation) {
        // Arrange
        resource.setOperation(excludedOperation);

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertFalse(shouldProcess);
    }
    
    @Test
    @DisplayName("Should process for non-user profile endpoints")
    void shouldProcessIfEnabled_NonUserProfileEndpoint_ReturnsTrue() {
        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);
        
        // Assert
        assertTrue(shouldProcess);
    }
    
    @Test
    @DisplayName("Should not process when resource is null")
    void shouldProcessIfEnabled_NullResource_ReturnsFalse() {
        // Arrange
        context.setResource(null);
        
        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);
        
        // Assert
        assertFalse(shouldProcess);
    }
    
    @Test
    @DisplayName("Should return correct default order")
    void getDefaultOrder_ReturnsCorrectValue() {
        assertEquals(50, processor.getDefaultOrder());
    }

    @Test
    @DisplayName("Should handle null response from createErrorResponse")
    void handleWebClientError_NullCreateErrorResponse_ReturnsDefaultResponse() {
        // Arrange
        AuthenticationProcessor spyProcessor = spy(processor);
        when(identityService.getUserProfile(any(HttpHeaders.class))).thenReturn(Mono.error(new RuntimeException("Authentication failed")));

        // Mock createErrorResponse to return null to test the fallback
        doReturn(null).when(spyProcessor).createErrorResponse(
                any(HttpHeaders.class),
                any(Resource.class),
                eq(HttpStatus.UNAUTHORIZED),
                eq("GW_ERR7"),
                anyString()
        );

        // Act
        Mono<ProcessingResult> result = spyProcessor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertFalse(processingResult.isContinueProcessing());
                assertNotNull(processingResult.getResponse());
                assertEquals(HttpStatus.UNAUTHORIZED, processingResult.getResponse().getStatusCode());
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("Should process when both operations are different from excluded ones")
    void shouldProcessIfEnabled_BothOperationsDifferent_ReturnsTrue() {
        // Arrange
        resource.setOperation("SOME_OTHER_OPERATION");

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertTrue(shouldProcess);
    }


}
