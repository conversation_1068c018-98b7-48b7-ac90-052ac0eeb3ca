package com.safaricom.dxl.partner.proxy.service;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.processor.RequestProcessor;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RequestPipelineTest {

    @Mock
    private RequestProcessor processor1;

    @Mock
    private RequestProcessor processor2;

    @Mock
    private RequestProcessor processor3;

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @Mock
    private ResponseUtils responseUtils;

    private RequestPipeline requestPipeline;
    private Mono<byte[]> body;

    @BeforeEach
    void setUp() {
        // Set up processors with different orders
        when(processor1.getOrder()).thenReturn(10);
        when(processor1.getName()).thenReturn("Processor1");
        when(processor1.shouldProcess(any())).thenReturn(true);

        when(processor2.getOrder()).thenReturn(20);
        when(processor2.getName()).thenReturn("Processor2");
        when(processor2.shouldProcess(any())).thenReturn(true);

        when(processor3.getOrder()).thenReturn(30);
        when(processor3.getName()).thenReturn("Processor3");
        when(processor3.shouldProcess(any())).thenReturn(true);

        // Set up request
        when(exchange.getRequest()).thenReturn(request);
        when(request.getMethod()).thenReturn(HttpMethod.GET);
        when(request.getHeaders()).thenReturn(HttpHeaders.EMPTY);

        // Set up body
        body = Mono.just(new byte[0]);

        // Create pipeline with processors
        List<RequestProcessor> processors = Arrays.asList(processor3, processor1, processor2);
        requestPipeline = new RequestPipeline(processors);
    }

    @Test
    @DisplayName("Should return sorted processors")
    void getSortedProcessors_ReturnsProcessorsSortedByOrder() {
        // Act
        List<RequestProcessor> sortedProcessors = requestPipeline.getSortedProcessors();

        // Assert
        Assertions.assertEquals(3, sortedProcessors.size());
        Assertions.assertEquals(processor1, sortedProcessors.get(0));
        Assertions.assertEquals(processor2, sortedProcessors.get(1));
        Assertions.assertEquals(processor3, sortedProcessors.get(2));
    }

    @Nested
    @DisplayName("process method tests")
    class ProcessTests {

        @Test
        @DisplayName("Should process request through all processors when all continue")
        void process_AllProcessorsContinue_ProcessesAllProcessors() {
            // Arrange
            when(processor1.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            when(processor2.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            ResponseEntity<byte[]> response = ResponseEntity.ok().build();
            when(processor3.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.terminate(response)));

            // Act
            Mono<ResponseEntity<byte[]>> result = requestPipeline.process(exchange, body);

            // Assert
            StepVerifier.create(result)
                .expectNext(response)
                .verifyComplete();

            verify(processor1).process(any(RequestContext.class));
            verify(processor2).process(any(RequestContext.class));
            verify(processor3).process(any(RequestContext.class));
        }

        @Test
        @DisplayName("Should throw error when all processors continue without termination")
        void process_AllProcessorsContinueWithoutTermination_ThrowsError() {
            // Arrange
            when(processor1.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            when(processor2.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            when(processor3.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            // Act
            Mono<ResponseEntity<byte[]>> result = requestPipeline.process(exchange, body);

            // Assert
            StepVerifier.create(result)
                .expectError(IllegalStateException.class)
                .verify();

            // We don't verify exact invocation counts because the error handling in the reactive chain
            // can cause processors to be invoked multiple times
            verify(processor1, atLeastOnce()).process(any(RequestContext.class));
            verify(processor2, atLeastOnce()).process(any(RequestContext.class));
            verify(processor3, atLeastOnce()).process(any(RequestContext.class));
        }

        @Test
        @DisplayName("Should stop processing when a processor terminates")
        void process_ProcessorTerminates_StopsProcessing() {
            // Arrange
            ResponseEntity<byte[]> response = ResponseEntity.ok().build();

            when(processor1.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            when(processor2.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.terminate(response)));

            // Act
            Mono<ResponseEntity<byte[]>> result = requestPipeline.process(exchange, body);

            // Assert
            StepVerifier.create(result)
                .expectNext(response)
                .verifyComplete();

            verify(processor1).process(any(RequestContext.class));
            verify(processor2).process(any(RequestContext.class));
            verify(processor3, never()).process(any(RequestContext.class));
        }

        @Test
        @DisplayName("Should skip disabled processors")
        void process_ProcessorDisabled_SkipsProcessor() {
            // Arrange
            when(processor2.shouldProcess(any())).thenReturn(false);

            when(processor1.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            ResponseEntity<byte[]> response = ResponseEntity.ok().build();
            when(processor3.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.terminate(response)));

            // Act
            Mono<ResponseEntity<byte[]>> result = requestPipeline.process(exchange, body);

            // Assert
            StepVerifier.create(result)
                .expectNextCount(1)
                .verifyComplete();

            verify(processor1).process(any(RequestContext.class));
            verify(processor2, never()).process(any(RequestContext.class));
            verify(processor3).process(any(RequestContext.class));
        }

        @Test
        @DisplayName("Should handle errors in processors")
        void process_ProcessorThrowsError_HandlesError() {
            // Arrange
            when(processor1.process(any(RequestContext.class)))
                .thenReturn(Mono.error(new RuntimeException("Test error")));

            ResponseEntity<byte[]> response = ResponseEntity.ok().build();
            when(processor2.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.terminate(response)));

            // Act
            Mono<ResponseEntity<byte[]>> result = requestPipeline.process(exchange, body);

            // Assert
            StepVerifier.create(result)
                .expectNext(response)
                .verifyComplete();

            verify(processor1).process(any(RequestContext.class));
            verify(processor2).process(any(RequestContext.class));
            verify(processor3, never()).process(any(RequestContext.class));
        }

        @Test
        @DisplayName("Should skip processors that should not process")
        void process_ProcessorShouldNotProcess_SkipsProcessor() {
            // Arrange
            when(processor1.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.continueProcessing()));

            when(processor2.shouldProcess(any(RequestContext.class))).thenReturn(false);

            ResponseEntity<byte[]> response = ResponseEntity.ok().build();
            when(processor3.process(any(RequestContext.class)))
                .thenReturn(Mono.just(ProcessingResult.terminate(response)));

            // Act
            Mono<ResponseEntity<byte[]>> result = requestPipeline.process(exchange, body);

            // Assert
            StepVerifier.create(result)
                .expectNextCount(1)
                .verifyComplete();

            verify(processor1).process(any(RequestContext.class));
            verify(processor2, never()).process(any(RequestContext.class));
            verify(processor3).process(any(RequestContext.class));
        }
    }
}
