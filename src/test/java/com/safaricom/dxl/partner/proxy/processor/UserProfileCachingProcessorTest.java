package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.ProcessorConfig;
import com.safaricom.dxl.partner.proxy.config.UserProfileConfig;
import com.safaricom.dxl.partner.proxy.model.CachedUserProfile;
import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.UserProfile;
import com.safaricom.dxl.partner.proxy.repository.CacheRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.ArrayList;

import static com.safaricom.dxl.partner.proxy.utils.MsUtilities.generateId;
import static com.safaricom.dxl.partner.proxy.utils.MsVariables.X_TOKEN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserProfileCachingProcessorTest {

    @Mock
    private CacheRepository cacheRepository;

    @Mock
    private UserProfileConfig userProfileConfig;

    @Mock
    private ProcessorConfig processorConfig;

    private UserProfileCachingProcessor processor;
    private UserProfile userProfile;
    private HttpHeaders headers;
    private static final String TEST_TOKEN = "test-token";
    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_PHONE = "254712345678";

    @BeforeEach
    void setUp() {
        processor = new UserProfileCachingProcessor(cacheRepository, userProfileConfig);
        processor.setProcessorConfig(processorConfig);

        // Set up headers
        headers = new HttpHeaders();
        headers.add(X_TOKEN, TEST_TOKEN);

        // Set up user profile
        userProfile = new UserProfile();
        userProfile.setEmail(TEST_EMAIL);
        userProfile.setPhoneNumber(TEST_PHONE);
        userProfile.setUsername("testuser");
        userProfile.setPermissions(new ArrayList<>());

        // Set up userProfileConfig to return a valid duration string
        lenient().when(userProfileConfig.getCacheDuration()).thenReturn("PT55M");

        // Default mock for cacheRepository.getData to avoid NPE
        lenient().when(cacheRepository.getData(anyString(), any())).thenReturn(Mono.empty());
    }

    @Test
    @DisplayName("Should have the correct order")
    void getDefaultOrder_ReturnsCorrectOrder() {
        assertEquals(70, processor.getDefaultOrder());
    }

    @Test
    @DisplayName("Should initialize properly")
    void init_LogsInitialization() {
        // Call the init method
        processor.init();

        // Verify that the processor is initialized by checking its state
        assertEquals(70, processor.getDefaultOrder());
        assertEquals("UserProfileCachingProcessor", processor.getName());
    }


    @Test
    @DisplayName("Should cache both email and phone independently when both are present")
    void process_CachesEmailAndPhoneIndependently() {
        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        // Set up cache repository to return empty for both keys
        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that getData and cacheData were called for both email and phone
        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq(generateId(TEST_TOKEN)), eq(TEST_EMAIL), any(Duration.class));
        verify(cacheRepository).cacheData(eq("phone" + generateId(TEST_TOKEN)), eq(TEST_PHONE), any(Duration.class));
    }

    @Test
    @DisplayName("Should only cache email if phone is missing")
    void process_CachesOnlyEmailIfPhoneMissing() {
        // Set up user profile with only email
        UserProfile emailOnlyProfile = new UserProfile();
        emailOnlyProfile.setEmail(TEST_EMAIL);
        emailOnlyProfile.setUsername("testuser");
        emailOnlyProfile.setPermissions(new ArrayList<>());

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(emailOnlyProfile);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq(generateId(TEST_TOKEN)), eq(TEST_EMAIL), any(Duration.class));
        verify(cacheRepository, never()).getData("phone" + generateId(TEST_TOKEN), String.class);
        verify(cacheRepository, never()).cacheData(startsWith("phone"), anyString(), any(Duration.class));
    }

    @Test
    @DisplayName("Should only cache phone if email is missing")
    void process_CachesOnlyPhoneIfEmailMissing() {
        // Set up user profile with only phone
        UserProfile phoneOnlyProfile = new UserProfile();
        phoneOnlyProfile.setPhoneNumber(TEST_PHONE);
        phoneOnlyProfile.setUsername("testuser");
        phoneOnlyProfile.setPermissions(new ArrayList<>());

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(phoneOnlyProfile);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq("phone" + generateId(TEST_TOKEN)), eq(TEST_PHONE), any(Duration.class));
        verify(cacheRepository, never()).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository, never()).cacheData(eq(generateId(TEST_TOKEN)), anyString(), any(Duration.class));
    }

    @Test
    @DisplayName("Should not cache if email is already cached")
    void process_EmailAlreadyCached_SkipsEmailCaching() {
        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        // Email already cached, phone not cached
        when(cacheRepository.getCachedData(anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            if (key.equals(generateId(TEST_TOKEN))) {
                return Mono.just(TEST_EMAIL);
            } else {
                return Mono.empty();
            }
        });
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository, never()).cacheData(eq(generateId(TEST_TOKEN)), anyString(), any(Duration.class));
        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq("phone" + generateId(TEST_TOKEN)), eq(TEST_PHONE), any(Duration.class));
    }

    @Test
    @DisplayName("Should not cache if phone is already cached")
    void process_PhoneAlreadyCached_SkipsPhoneCaching() {
        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        // Phone already cached, email not cached
        when(cacheRepository.getCachedData(anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            if (key.equals("phone" + generateId(TEST_TOKEN))) {
                return Mono.just(TEST_PHONE);
            } else {
                return Mono.empty();
            }
        });
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq(generateId(TEST_TOKEN)), eq(TEST_EMAIL), any(Duration.class));
        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository, never()).cacheData(eq("phone" + generateId(TEST_TOKEN)), anyString(), any(Duration.class));
    }

    @Test
    @DisplayName("Should continue processing if caching throws error")
    void process_CachingThrowsError_ContinuesProcessing() {
        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.error(new RuntimeException("cache error")));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq(generateId(TEST_TOKEN)), eq(TEST_EMAIL), any(Duration.class));
        verify(cacheRepository).cacheData(eq("phone" + generateId(TEST_TOKEN)), eq(TEST_PHONE), any(Duration.class));
    }

    @Test
    @DisplayName("Should handle missing token")
    void process_MissingToken_ContinuesProcessing() {
        // Set up headers without token
        HttpHeaders headersWithoutToken = new HttpHeaders();

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headersWithoutToken);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that getData and setData were never called
        verify(cacheRepository, never()).getData(anyString(), any());
        verify(cacheRepository, never()).setData(anyString(), any(CachedUserProfile.class), any(Duration.class));
    }

    @Test
    @DisplayName("Should handle missing user profile")
    void process_MissingUserProfile_ContinuesProcessing() {
        // Set up context without user profile
        RequestContext context = mock(RequestContext.class);
        when(context.getUserProfile()).thenReturn(null);

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that getData and setData were never called
        verify(cacheRepository, never()).getData(anyString(), any());
        verify(cacheRepository, never()).setData(anyString(), any(CachedUserProfile.class), any(Duration.class));
    }

    @Test
    @DisplayName("Should handle missing email and phone")
    void process_MissingEmailAndPhone_ContinuesProcessing() {
        // Set up user profile without email and phone
        UserProfile userProfileWithoutData = new UserProfile();
        userProfileWithoutData.setUsername("testuser");
        userProfileWithoutData.setPermissions(new ArrayList<>());

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfileWithoutData);

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that getData and setData were never called since we skip caching when both email and phone are empty
        verify(cacheRepository, never()).getData(anyString(), any());
        verify(cacheRepository, never()).setData(anyString(), any(CachedUserProfile.class), any(Duration.class));
    }

    @Test
    @DisplayName("Should handle token with Bearer prefix")
    void process_TokenWithBearerPrefix_RemovesBearerPrefix() {
        // Set up headers with Bearer prefix
        HttpHeaders headersWithBearer = new HttpHeaders();
        headersWithBearer.add(X_TOKEN, "Bearer " + TEST_TOKEN);

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headersWithBearer);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that the Bearer prefix was removed from the token when generating cache keys
        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq(generateId(TEST_TOKEN)), eq(TEST_EMAIL), any(Duration.class));
        verify(cacheRepository).cacheData(eq("phone" + generateId(TEST_TOKEN)), eq(TEST_PHONE), any(Duration.class));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"invalid-duration"})
    @DisplayName("Should use default duration of 1 hour for null, empty, or invalid cache duration config")
    void process_CacheDurationEdgeCases_UsesDefaultDuration(String cacheDuration) {
        // Set up userProfileConfig to return parameterized duration
        when(userProfileConfig.getCacheDuration()).thenReturn(cacheDuration);

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that caching still works with default duration (1 hour)
        verify(cacheRepository).cacheData(generateId(TEST_TOKEN), TEST_EMAIL, Duration.ofHours(1));
        verify(cacheRepository).cacheData("phone" + generateId(TEST_TOKEN), TEST_PHONE, Duration.ofHours(1));
    }

    @Test
    @DisplayName("Should handle empty email string")
    void process_EmptyEmailString_SkipsEmailCaching() {
        // Set up user profile with empty email
        UserProfile userProfileWithEmptyEmail = new UserProfile();
        userProfileWithEmptyEmail.setEmail(""); // Empty string
        userProfileWithEmptyEmail.setPhoneNumber(TEST_PHONE);
        userProfileWithEmptyEmail.setUsername("testuser");
        userProfileWithEmptyEmail.setPermissions(new ArrayList<>());

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfileWithEmptyEmail);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that only phone caching was attempted
        verify(cacheRepository, never()).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository, never()).cacheData(eq(generateId(TEST_TOKEN)), anyString(), any(Duration.class));
        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq("phone" + generateId(TEST_TOKEN)), eq(TEST_PHONE), any(Duration.class));
    }

    @Test
    @DisplayName("Should handle empty phone string")
    void process_EmptyPhoneString_SkipsPhoneCaching() {
        // Set up user profile with empty phone
        UserProfile userProfileWithEmptyPhone = new UserProfile();
        userProfileWithEmptyPhone.setEmail(TEST_EMAIL);
        userProfileWithEmptyPhone.setPhoneNumber(""); // Empty string
        userProfileWithEmptyPhone.setUsername("testuser");
        userProfileWithEmptyPhone.setPermissions(new ArrayList<>());

        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfileWithEmptyPhone);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(true));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that only email caching was attempted
        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq(generateId(TEST_TOKEN)), eq(TEST_EMAIL), any(Duration.class));
        verify(cacheRepository, never()).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository, never()).cacheData(startsWith("phone"), anyString(), any(Duration.class));
    }

    @Test
    @DisplayName("Should handle cache operation returning false")
    void process_CacheOperationReturnsFalse_ContinuesProcessing() {
        // Set up request and exchange
        ServerHttpRequest request = mock(ServerHttpRequest.class);
        ServerWebExchange exchange = mock(ServerWebExchange.class);
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up context
        RequestContext context = new RequestContext(exchange, Mono.empty());
        context.setUserProfile(userProfile);

        when(cacheRepository.getCachedData(anyString())).thenReturn(Mono.empty());
        when(cacheRepository.cacheData(anyString(), anyString(), any(Duration.class))).thenReturn(Mono.just(false)); // Return false

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
            .verifyComplete();

        // Verify that caching was attempted even though it returned false
        verify(cacheRepository).getCachedData(generateId(TEST_TOKEN));
        verify(cacheRepository).getCachedData("phone" + generateId(TEST_TOKEN));
        verify(cacheRepository).cacheData(eq(generateId(TEST_TOKEN)), eq(TEST_EMAIL), any(Duration.class));
        verify(cacheRepository).cacheData(eq("phone" + generateId(TEST_TOKEN)), eq(TEST_PHONE), any(Duration.class));
    }

    @Test
    @DisplayName("Should always return true for shouldProcessIfEnabled")
    void shouldProcessIfEnabled_AlwaysReturnsTrue() {
        // Set up a mock context
        RequestContext context = mock(RequestContext.class);

        // Act & Assert
        assertTrue(processor.shouldProcessIfEnabled(context));
    }
}
