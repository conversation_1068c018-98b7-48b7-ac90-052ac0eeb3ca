package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.UserProfile;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.ES;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AuthorizationProcessorTest {

    @Mock
    private ResponseUtils responseUtils;

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @InjectMocks
    private AuthorizationProcessor processor;

    private RequestContext context;
    private Resource resource;
    private UserProfile userProfile;
    private HttpHeaders headers;

    @BeforeEach
    void setUp() {
        // Set up headers
        headers = new HttpHeaders();

        // Set up request
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up resource
        resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/users");
        resource.setEndpoint("http://user-service/api/users");
        resource.setOperation("GET_USERS");
        resource.setPermission("READ_USERS");

        // Set up user profile
        userProfile = new UserProfile();
        userProfile.setUsername("testuser");
        List<String> permissions = new ArrayList<>();
        permissions.add("READ_USERS");
        userProfile.setPermissions(permissions);

        // Set up context
        context = new RequestContext(exchange, Mono.empty());
        context.setResource(resource);
        context.setUserProfile(userProfile);
    }

    @Test
    @DisplayName("Should authorize user with required permission and continue processing")
    void process_UserHasPermission_ContinuesProcessing() {
        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertTrue(processingResult.isContinueProcessing());
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("Should return error when user lacks required permission")
    void process_UserLacksPermission_ReturnsError() {
        // Arrange
        userProfile.setPermissions(Collections.emptyList());

        ResponseEntity<byte[]> errorResponse = ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        when(responseUtils.errorResponseReactive(
                any(HttpHeaders.class),
                eq(resource),
                eq(HttpStatus.UNAUTHORIZED),
                eq("GW_ERR7"),
                eq(ES)
        )).thenReturn(Mono.just(errorResponse));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertFalse(processingResult.isContinueProcessing());
                assertEquals(errorResponse, processingResult.getResponse());
            })
            .verifyComplete();

        verify(responseUtils).errorResponseReactive(
                any(HttpHeaders.class),
                eq(resource),
                eq(HttpStatus.UNAUTHORIZED),
                eq("GW_ERR7"),
                eq(ES)
        );
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"   ", "\t", "\n"})
    @DisplayName("Should not process when resource has null, empty, or blank permission")
    void shouldProcessIfEnabled_InvalidPermission_ReturnsFalse(String permission) {
        // Arrange
        resource.setPermission(permission);

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertFalse(shouldProcess);
    }

    @Test
    @DisplayName("Should not process when resource is user profile endpoint")
    void shouldProcessIfEnabled_UserProfileEndpoint_ReturnsFalse() {
        // Arrange
        resource.setOperation(GET_LOGGED_IN_USER_PROFILE);

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertFalse(shouldProcess);
    }

    @Test
    @DisplayName("Should not process when resource has permission but is user profile endpoint")
    void shouldProcessIfEnabled_ValidPermissionButUserProfileEndpoint_ReturnsFalse() {
        // Arrange
        resource.setPermission("READ_PROFILE"); // Valid permission
        resource.setOperation(GET_LOGGED_IN_USER_PROFILE); // But it's the user profile endpoint

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertFalse(shouldProcess);
    }

    @Test
    @DisplayName("Should not process when resource is null")
    void shouldProcessIfEnabled_NullResource_ReturnsFalse() {
        // Arrange
        context.setResource(null);

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertFalse(shouldProcess);
    }

    @Test
    @DisplayName("Should return correct default order")
    void getDefaultOrder_ReturnsCorrectValue() {
        assertEquals(60, processor.getDefaultOrder());
    }
}
