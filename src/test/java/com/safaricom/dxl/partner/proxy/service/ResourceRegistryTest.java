package com.safaricom.dxl.partner.proxy.service;

import com.safaricom.dxl.partner.proxy.config.BasicAuthConfig;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.model.ResourceConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ResourceRegistryTest {

    @Mock
    private BasicAuthConfig basicAuthConfig;

    private ResourceRegistry resourceRegistry;
    private List<ResourceConfig> validResourceConfigs;

    @BeforeEach
    void setUp() {
        // Mock the basicAuthConfig
        Map<String, String> credentials = new HashMap<>();
        credentials.put("development", "dXNlcjpwYXNz");
        Mockito.lenient().when(basicAuthConfig.getCredentials()).thenReturn(credentials);

        resourceRegistry = new ResourceRegistry(basicAuthConfig);

        // Set up valid resource configs
        validResourceConfigs = new ArrayList<>();

        // Create first group
        ResourceConfig group1 = new ResourceConfig();
        group1.setGroup("api");

        List<Resource> resources1 = new ArrayList<>();
        Resource resource1 = new Resource();
        resource1.setMethod("GET");
        resource1.setPath("/users");
        resource1.setEndpoint("http://user-service/api/users");
        resource1.setOperation("GET_USERS");
        resource1.setMicroservice("user-service");
        resource1.setAuthType("basic");
        resource1.setBasicAuthCredentials("development");
        resources1.add(resource1);

        group1.setResources(resources1);
        validResourceConfigs.add(group1);
    }

    @Test
    @DisplayName("Should store resources when valid configurations are provided")
    void validateAndStoreResources_ValidConfigs_StoresResources() {
        // Act
        resourceRegistry.validateAndStoreResources(validResourceConfigs);

        // Assert
        List<Resource> storedResources = resourceRegistry.getResources();
        assertEquals(1, storedResources.size());
        assertEquals("GET_USERS", storedResources.get(0).getOperation());
        assertEquals("/api/users", storedResources.get(0).getFullPath());
    }

    @Test
    @DisplayName("Should throw exception when duplicate group is found")
    void validateAndStoreResources_DuplicateGroup_ThrowsException() {
        // Arrange
        ResourceConfig duplicateGroup = new ResourceConfig();
        duplicateGroup.setGroup("api");

        Resource resource = new Resource();
        resource.setMethod("POST");
        resource.setPath("/users");
        resource.setEndpoint("http://user-service/api/users");
        resource.setOperation("CREATE_USER");
        resource.setMicroservice("user-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        duplicateGroup.setResources(List.of(resource));
        validResourceConfigs.add(duplicateGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when duplicate operation is found")
    void validateAndStoreResources_DuplicateOperation_ThrowsException() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("admin");

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/admin-users");
        resource.setEndpoint("http://admin-service/users");
        resource.setOperation("GET_USERS"); // Same operation as in first group
        resource.setMicroservice("admin-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when duplicate method+path combination is found in same group")
    void validateAndStoreResources_DuplicateMethodPath_ThrowsException() {
        // Arrange
        Resource duplicateResource = new Resource();
        duplicateResource.setMethod("GET");
        duplicateResource.setPath("/users"); // Same method+path as resource1
        duplicateResource.setEndpoint("http://user-service/api/users/v2");
        duplicateResource.setOperation("GET_USERS_V2");
        duplicateResource.setMicroservice("user-service");
        duplicateResource.setAuthType("basic");
        duplicateResource.setBasicAuthCredentials("development");

        validResourceConfigs.get(0).getResources().add(duplicateResource);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when group name is invalid")
    void validateAndStoreResources_InvalidGroupName_ThrowsException() {
        // Arrange
        ResourceConfig invalidGroup = new ResourceConfig();
        invalidGroup.setGroup("invalid@group"); // Contains invalid character

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/test");
        resource.setEndpoint("http://test-service/api");
        resource.setOperation("TEST");
        resource.setMicroservice("test-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        invalidGroup.setResources(List.of(resource));
        validResourceConfigs.add(invalidGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when HTTP method is invalid")
    void validateAndStoreResources_InvalidMethod_ThrowsException() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("test");

        Resource resource = new Resource();
        resource.setMethod("INVALID_METHOD"); // Invalid HTTP method
        resource.setPath("/test");
        resource.setEndpoint("http://test-service/api");
        resource.setOperation("TEST");
        resource.setMicroservice("test-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when path is invalid")
    void validateAndStoreResources_InvalidPath_ThrowsException() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("test");

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/test/{id{nested}}"); // Invalid path with nested placeholders
        resource.setEndpoint("http://test-service/api");
        resource.setOperation("TEST");
        resource.setMicroservice("test-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when endpoint URL is invalid")
    void validateAndStoreResources_InvalidEndpoint_ThrowsException() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("test");

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/test");
        resource.setEndpoint("http://invalid url with spaces"); // Invalid URL
        resource.setOperation("TEST");
        resource.setMicroservice("test-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when operation is blank")
    void validateAndStoreResources_BlankOperation_ThrowsException() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("test");

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/test");
        resource.setEndpoint("http://test-service/api");
        resource.setOperation(""); // Blank operation
        resource.setMicroservice("test-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when auth type is invalid")
    void validateAndStoreResources_InvalidAuthType_ThrowsException() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("test");

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/test");
        resource.setEndpoint("http://test-service/api");
        resource.setOperation("TEST");
        resource.setMicroservice("test-service");
        resource.setAuthType("invalid"); // Invalid auth type
        resource.setBasicAuthCredentials("development");

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should throw exception when basic auth credentials are invalid")
    void validateAndStoreResources_InvalidBasicAuthCredentials_ThrowsException() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("test");

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/test");
        resource.setEndpoint("http://test-service/api");
        resource.setOperation("TEST");
        resource.setMicroservice("test-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("invalid"); // Invalid credentials key

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> resourceRegistry.validateAndStoreResources(validResourceConfigs));
    }

    @Test
    @DisplayName("Should validate path with valid placeholders")
    void validateAndStoreResources_PathWithValidPlaceholders_Succeeds() {
        // Arrange
        ResourceConfig newGroup = new ResourceConfig();
        newGroup.setGroup("test");

        Resource resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/users/{userId}/profiles/{profileId}"); // Valid path with placeholders
        resource.setEndpoint("http://user-service/api/users/{userId}/profiles/{profileId}");
        resource.setOperation("GET_USER_PROFILE");
        resource.setMicroservice("user-service");
        resource.setAuthType("basic");
        resource.setBasicAuthCredentials("development");

        newGroup.setResources(List.of(resource));
        validResourceConfigs.add(newGroup);

        // Act
        resourceRegistry.validateAndStoreResources(validResourceConfigs);

        // Assert
        List<Resource> storedResources = resourceRegistry.getResources();
        assertEquals(2, storedResources.size());
        assertEquals("GET_USER_PROFILE", storedResources.get(1).getOperation());
        assertEquals("/test/users/{userId}/profiles/{profileId}", storedResources.get(1).getFullPath());
    }

    @Nested
    @DisplayName("Private Method Tests")
    class PrivateMethodTests {

        @Test
        @DisplayName("isValidMethod should return true for valid HTTP methods")
        void isValidMethod_ValidMethod_ReturnsTrue() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidMethod", String.class);
            method.setAccessible(true);

            // Act & Assert
            for (HttpMethod httpMethod : HttpMethod.values()) {
                assertTrue((Boolean) method.invoke(resourceRegistry, httpMethod.name()));
            }
        }

        @Test
        @DisplayName("isValidMethod should return false for null method")
        void isValidMethod_NullMethod_ReturnsFalse() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidMethod", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertFalse((Boolean) method.invoke(resourceRegistry, (Object) null));
        }

        @Test
        @DisplayName("isValidMethod should return false for invalid method")
        void isValidMethod_InvalidMethod_ReturnsFalse() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidMethod", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertFalse((Boolean) method.invoke(resourceRegistry, "INVALID_METHOD"));
        }

        @ParameterizedTest
        @ValueSource(strings = {"/valid", "/valid/path", "/valid/{param}", "/valid/{param}/path"})
        @DisplayName("isValidPath should return true for valid paths")
        void isValidPath_ValidPath_ReturnsTrue(String path) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidPath", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertTrue((Boolean) method.invoke(resourceRegistry, path));
        }

        @ParameterizedTest
        @ValueSource(strings = {"/invalid/{param{nested}}", "/invalid/{param}/path{nested}"})
        @DisplayName("isValidPath should return false for invalid paths")
        void isValidPath_InvalidPath_ReturnsFalse(String path) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidPath", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertFalse((Boolean) method.invoke(resourceRegistry, path));
        }

        @ParameterizedTest
        @ValueSource(strings = {"http://valid.com", "https://valid.com/path", "http://valid.com/{param}", "https://valid.com/{param}/path"})
        @DisplayName("isValidUrl should return true for valid URLs")
        void isValidUrl_ValidUrl_ReturnsTrue(String url) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidUrl", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertTrue((Boolean) method.invoke(resourceRegistry, url));
        }

        @ParameterizedTest
        @ValueSource(strings = {"invalid url", "http://invalid.com/{param{nested}}", "http://invalid.com/path{nested}"})
        @DisplayName("isValidUrl should return false for invalid URLs")
        void isValidUrl_InvalidUrl_ReturnsFalse(String url) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidUrl", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertFalse((Boolean) method.invoke(resourceRegistry, url));
        }

        @Test
        @DisplayName("isValidUrl should return false for URL with syntax exception")
        void isValidUrl_UrlWithSyntaxException_ReturnsFalse() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("isValidUrl", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertFalse((Boolean) method.invoke(resourceRegistry, "http://invalid url with spaces"));
        }

        @ParameterizedTest
        @ValueSource(strings = {"basic", "BASIC", "bearer", "BEARER"})
        @DisplayName("validateAuthType should not throw exception for valid auth types")
        void validateAuthType_ValidAuthType_DoesNotThrowException(String authType) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateAuthType", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertDoesNotThrow(() -> method.invoke(resourceRegistry, authType));
        }

        @ParameterizedTest
        @ValueSource(strings = {"invalid", "oauth", "digest"})
        @DisplayName("validateAuthType should throw exception for invalid auth types")
        void validateAuthType_InvalidAuthType_ThrowsException(String authType) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateAuthType", String.class);
            method.setAccessible(true);

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, authType));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
        }

        @Test
        @DisplayName("validateBasicAuthCredentials should not throw exception for valid credentials")
        void validateBasicAuthCredentials_ValidCredentials_DoesNotThrowException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateBasicAuthCredentials", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertDoesNotThrow(() -> method.invoke(resourceRegistry, "development"));
        }

        @Test
        @DisplayName("validateBasicAuthCredentials should throw exception for invalid credentials")
        void validateBasicAuthCredentials_InvalidCredentials_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateBasicAuthCredentials", String.class);
            method.setAccessible(true);

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, "invalid"));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
        }

        @ParameterizedTest
        @ValueSource(strings = {"valid", "valid-group", "123", "group123"})
        @DisplayName("validateGroupName should not throw exception for valid group names")
        void validateGroupName_ValidGroupName_DoesNotThrowException(String groupName) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateGroupName", String.class);
            method.setAccessible(true);

            // Act & Assert
            assertDoesNotThrow(() -> method.invoke(resourceRegistry, groupName));
        }

        @ParameterizedTest
        @ValueSource(strings = {"invalid@group", "invalid group", "invalid_group", "invalid#group"})
        @DisplayName("validateGroupName should throw exception for invalid group names")
        void validateGroupName_InvalidGroupName_ThrowsException(String groupName) throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateGroupName", String.class);
            method.setAccessible(true);

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, groupName));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
        }

        @Test
        @DisplayName("validateGroups should validate all groups and resources")
        void validateGroups_ValidGroups_DoesNotThrowException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateGroups", List.class);
            method.setAccessible(true);

            // Act & Assert
            assertDoesNotThrow(() -> method.invoke(resourceRegistry, validResourceConfigs));
        }

        @Test
        @DisplayName("validateGroups should throw exception for duplicate group")
        void validateGroups_DuplicateGroup_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateGroups", List.class);
            method.setAccessible(true);

            // Create duplicate group
            ResourceConfig duplicateGroup = new ResourceConfig();
            duplicateGroup.setGroup("api"); // Same as existing group

            Resource resource = new Resource();
            resource.setMethod("POST");
            resource.setPath("/users");
            resource.setEndpoint("http://user-service/api/users");
            resource.setOperation("CREATE_USER");
            resource.setMicroservice("user-service");
            resource.setAuthType("basic");
            resource.setBasicAuthCredentials("development");

            duplicateGroup.setResources(List.of(resource));

            List<ResourceConfig> configsWithDuplicate = new ArrayList<>(validResourceConfigs);
            configsWithDuplicate.add(duplicateGroup);

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, configsWithDuplicate));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            assertTrue(exception.getCause().getMessage().contains("Duplicate group found"));
        }

        @Test
        @DisplayName("validateGroups should throw exception for duplicate operation")
        void validateGroups_DuplicateOperation_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateGroups", List.class);
            method.setAccessible(true);

            // Create group with duplicate operation
            ResourceConfig newGroup = new ResourceConfig();
            newGroup.setGroup("admin");

            Resource resource = new Resource();
            resource.setMethod("GET");
            resource.setPath("/admin-users");
            resource.setEndpoint("http://admin-service/users");
            resource.setOperation("GET_USERS"); // Same operation as in first group
            resource.setMicroservice("admin-service");
            resource.setAuthType("basic");
            resource.setBasicAuthCredentials("development");

            newGroup.setResources(List.of(resource));

            List<ResourceConfig> configsWithDuplicateOp = new ArrayList<>(validResourceConfigs);
            configsWithDuplicateOp.add(newGroup);

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, configsWithDuplicateOp));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            assertTrue(exception.getCause().getMessage().contains("Duplicate operation found"));
        }

        @Test
        @DisplayName("validateGroups should throw exception for duplicate method+path in same group")
        void validateGroups_DuplicateMethodPath_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateGroups", List.class);
            method.setAccessible(true);

            // Create resource config with duplicate method+path in same group
            ResourceConfig group = validResourceConfigs.get(0);

            Resource duplicateResource = new Resource();
            duplicateResource.setMethod("GET");
            duplicateResource.setPath("/users"); // Same method+path as existing resource
            duplicateResource.setEndpoint("http://user-service/api/users/v2");
            duplicateResource.setOperation("GET_USERS_V2");
            duplicateResource.setMicroservice("user-service");
            duplicateResource.setAuthType("basic");
            duplicateResource.setBasicAuthCredentials("development");

            List<Resource> resourcesWithDuplicate = new ArrayList<>(group.getResources());
            resourcesWithDuplicate.add(duplicateResource);

            ResourceConfig modifiedGroup = new ResourceConfig();
            modifiedGroup.setGroup(group.getGroup());
            modifiedGroup.setResources(resourcesWithDuplicate);

            List<ResourceConfig> configsWithDuplicateMethodPath = new ArrayList<>();
            configsWithDuplicateMethodPath.add(modifiedGroup);

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, configsWithDuplicateMethodPath));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            assertTrue(exception.getCause().getMessage().contains("Duplicate method+path combination found"));
        }

        @Test
        @DisplayName("validateResource should validate all resource properties")
        void validateResource_ValidResource_DoesNotThrowException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateResource", Resource.class);
            method.setAccessible(true);

            Resource validResource = validResourceConfigs.get(0).getResources().get(0);

            // Act & Assert
            assertDoesNotThrow(() -> method.invoke(resourceRegistry, validResource));
        }

        @Test
        @DisplayName("validateResource should throw exception for invalid method")
        void validateResource_InvalidMethod_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateResource", Resource.class);
            method.setAccessible(true);

            Resource invalidResource = new Resource();
            invalidResource.setMethod("INVALID_METHOD");
            invalidResource.setPath("/test");
            invalidResource.setEndpoint("http://test-service/api");
            invalidResource.setOperation("TEST");
            invalidResource.setMicroservice("test-service");
            invalidResource.setAuthType("basic");
            invalidResource.setBasicAuthCredentials("development");

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, invalidResource));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            assertTrue(exception.getCause().getMessage().contains("Invalid HTTP method"));
        }

        @Test
        @DisplayName("validateResource should throw exception for invalid path")
        void validateResource_InvalidPath_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateResource", Resource.class);
            method.setAccessible(true);

            Resource invalidResource = new Resource();
            invalidResource.setMethod("GET");
            invalidResource.setPath("/test/{id{nested}}"); // Invalid path
            invalidResource.setEndpoint("http://test-service/api");
            invalidResource.setOperation("TEST");
            invalidResource.setMicroservice("test-service");
            invalidResource.setAuthType("basic");
            invalidResource.setBasicAuthCredentials("development");

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, invalidResource));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            assertTrue(exception.getCause().getMessage().contains("Invalid resource path"));
        }

        @Test
        @DisplayName("validateResource should throw exception for invalid endpoint URL")
        void validateResource_InvalidEndpoint_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateResource", Resource.class);
            method.setAccessible(true);

            Resource invalidResource = new Resource();
            invalidResource.setMethod("GET");
            invalidResource.setPath("/test");
            invalidResource.setEndpoint("http://invalid url with spaces"); // Invalid URL
            invalidResource.setOperation("TEST");
            invalidResource.setMicroservice("test-service");
            invalidResource.setAuthType("basic");
            invalidResource.setBasicAuthCredentials("development");

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, invalidResource));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            assertTrue(exception.getCause().getMessage().contains("Invalid endpoint URL"));
        }

        @Test
        @DisplayName("validateResource should throw exception for blank operation")
        void validateResource_BlankOperation_ThrowsException() throws Exception {
            // Arrange
            Method method = ResourceRegistry.class.getDeclaredMethod("validateResource", Resource.class);
            method.setAccessible(true);

            Resource invalidResource = new Resource();
            invalidResource.setMethod("GET");
            invalidResource.setPath("/test");
            invalidResource.setEndpoint("http://test-service/api");
            invalidResource.setOperation(""); // Blank operation
            invalidResource.setMicroservice("test-service");
            invalidResource.setAuthType("basic");
            invalidResource.setBasicAuthCredentials("development");

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> method.invoke(resourceRegistry, invalidResource));
            assertTrue(exception.getCause() instanceof IllegalArgumentException);
            assertTrue(exception.getCause().getMessage().contains("Operation cannot be null or blank"));
        }
    }
}
