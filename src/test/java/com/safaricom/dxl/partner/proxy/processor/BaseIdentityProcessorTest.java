package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for BaseIdentityProcessor.
 * Tests all methods including initialization, error response creation, and inheritance behavior.
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class BaseIdentityProcessorTest {

    @Mock
    private ResponseUtils responseUtils;

    @Mock
    private Resource resource;

    private TestableBaseIdentityProcessor processor;
    private HttpHeaders headers;

    @BeforeEach
    void setUp() {
        processor = new TestableBaseIdentityProcessor(responseUtils);
        headers = new HttpHeaders();
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("Should create instance with valid ResponseUtils dependency")
        void constructor_WithValidResponseUtils_CreatesInstance() {
            // When
            TestableBaseIdentityProcessor testProcessor = new TestableBaseIdentityProcessor(responseUtils);

            // Then
            assertNotNull(testProcessor);
            assertNotNull(testProcessor.responseUtils);
            assertEquals(responseUtils, testProcessor.responseUtils);
        }

        @Test
        @DisplayName("Should accept null ResponseUtils without throwing exception")
        void constructor_WithNullResponseUtils_CreatesInstance() {
            // When & Then - Should not throw exception
            TestableBaseIdentityProcessor testProcessor = new TestableBaseIdentityProcessor(null);
            
            assertNotNull(testProcessor);
            assertNull(testProcessor.responseUtils);
        }
    }

    @Nested
    @DisplayName("Initialization Tests")
    class InitializationTests {

        @Test
        @DisplayName("Should initialize processor successfully")
        void init_WhenCalled_LogsInitialization() {
            // When
            processor.init();

            // Then - No exception should be thrown
            // Logging is tested through integration tests
            assertNotNull(processor);
        }

        @Test
        @DisplayName("Should return correct processor name")
        void getName_WhenCalled_ReturnsCorrectName() {
            // When
            String name = processor.getName();

            // Then
            assertEquals("TestableBaseIdentityProcessor", name);
        }
    }

    @Nested
    @DisplayName("Error Response Creation Tests")
    class ErrorResponseCreationTests {

        @Test
        @DisplayName("Should create error response successfully")
        void createErrorResponse_WithValidParameters_ReturnsErrorResponse() {
            // Given
            HttpStatus status = HttpStatus.BAD_REQUEST;
            String errorCode = "ERR001";
            String errorMessage = "Test error message";
            
            ResponseEntity<byte[]> expectedResponse = ResponseEntity.status(status).body("error".getBytes());
            when(responseUtils.errorResponseReactive(headers, resource, status, errorCode, errorMessage))
                    .thenReturn(Mono.just(expectedResponse));

            // When
            Mono<ResponseEntity<byte[]>> result = processor.createErrorResponse(headers, resource, status, errorCode, errorMessage);

            // Then
            StepVerifier.create(result)
                    .expectNext(expectedResponse)
                    .verifyComplete();

            verify(responseUtils).errorResponseReactive(headers, resource, status, errorCode, errorMessage);
        }

        @Test
        @DisplayName("Should handle null headers gracefully")
        void createErrorResponse_WithNullHeaders_CallsResponseUtils() {
            // Given
            HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
            String errorCode = "ERR002";
            String errorMessage = "Internal error";
            
            ResponseEntity<byte[]> expectedResponse = ResponseEntity.status(status).body("error".getBytes());
            when(responseUtils.errorResponseReactive(null, resource, status, errorCode, errorMessage))
                    .thenReturn(Mono.just(expectedResponse));

            // When
            Mono<ResponseEntity<byte[]>> result = processor.createErrorResponse(null, resource, status, errorCode, errorMessage);

            // Then
            StepVerifier.create(result)
                    .expectNext(expectedResponse)
                    .verifyComplete();

            verify(responseUtils).errorResponseReactive(null, resource, status, errorCode, errorMessage);
        }

        @Test
        @DisplayName("Should handle null resource gracefully")
        void createErrorResponse_WithNullResource_CallsResponseUtils() {
            // Given
            HttpStatus status = HttpStatus.NOT_FOUND;
            String errorCode = "ERR003";
            String errorMessage = "Resource not found";
            
            ResponseEntity<byte[]> expectedResponse = ResponseEntity.status(status).body("error".getBytes());
            when(responseUtils.errorResponseReactive(headers, null, status, errorCode, errorMessage))
                    .thenReturn(Mono.just(expectedResponse));

            // When
            Mono<ResponseEntity<byte[]>> result = processor.createErrorResponse(headers, null, status, errorCode, errorMessage);

            // Then
            StepVerifier.create(result)
                    .expectNext(expectedResponse)
                    .verifyComplete();

            verify(responseUtils).errorResponseReactive(headers, null, status, errorCode, errorMessage);
        }

        @Test
        @DisplayName("Should handle empty error message")
        void createErrorResponse_WithEmptyErrorMessage_CallsResponseUtils() {
            // Given
            HttpStatus status = HttpStatus.BAD_REQUEST;
            String errorCode = "ERR004";
            String errorMessage = "";
            
            ResponseEntity<byte[]> expectedResponse = ResponseEntity.status(status).body("error".getBytes());
            when(responseUtils.errorResponseReactive(headers, resource, status, errorCode, errorMessage))
                    .thenReturn(Mono.just(expectedResponse));

            // When
            Mono<ResponseEntity<byte[]>> result = processor.createErrorResponse(headers, resource, status, errorCode, errorMessage);

            // Then
            StepVerifier.create(result)
                    .expectNext(expectedResponse)
                    .verifyComplete();

            verify(responseUtils).errorResponseReactive(headers, resource, status, errorCode, errorMessage);
        }

        @Test
        @DisplayName("Should propagate error from ResponseUtils")
        void createErrorResponse_WithResponseUtilsError_PropagatesError() {
            // Given
            HttpStatus status = HttpStatus.SERVICE_UNAVAILABLE;
            String errorCode = "ERR005";
            String errorMessage = "Service unavailable";
            
            when(responseUtils.errorResponseReactive(headers, resource, status, errorCode, errorMessage))
                    .thenReturn(Mono.error(new RuntimeException("ResponseUtils error")));

            // When
            Mono<ResponseEntity<byte[]>> result = processor.createErrorResponse(headers, resource, status, errorCode, errorMessage);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(responseUtils).errorResponseReactive(headers, resource, status, errorCode, errorMessage);
        }
    }

    @Nested
    @DisplayName("Inheritance Tests")
    class InheritanceTests {

        @Test
        @DisplayName("Should extend AbstractRequestProcessor")
        void class_ShouldExtendAbstractRequestProcessor() {
            // Then
            assertTrue(processor instanceof AbstractRequestProcessor);
        }

        @Test
        @DisplayName("Should have access to inherited methods")
        void processor_ShouldHaveAccessToInheritedMethods() {
            // When & Then - These methods are inherited from AbstractRequestProcessor
            assertNotNull(processor.getName());
            assertTrue(processor.getOrder() >= 0);
        }
    }

    /**
     * Testable concrete implementation of BaseIdentityProcessor for testing purposes.
     * Since BaseIdentityProcessor is abstract, we need a concrete implementation to test it.
     */
    private static class TestableBaseIdentityProcessor extends BaseIdentityProcessor {

        public TestableBaseIdentityProcessor(ResponseUtils responseUtils) {
            super(responseUtils);
        }

        @Override
        protected boolean shouldProcessIfEnabled(RequestContext context) {
            return true; // Simple implementation for testing
        }

        @Override
        public Mono<ProcessingResult> process(RequestContext context) {
            return Mono.just(ProcessingResult.continueProcessing()); // Simple implementation for testing
        }

        @Override
        protected int getDefaultOrder() {
            return 100; // Test order
        }
    }
}
