package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.model.*;
import com.safaricom.dxl.partner.proxy.service.IdentityService;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import com.safaricom.dxl.webflux.starter.model.WsHeader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.safaricom.dxl.partner.proxy.utils.MsVariables.GET_LOGGED_IN_USER_PROFILE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class UserProfileProcessorTest {

    @Mock
    private IdentityService identityService;

    @Mock
    private ResponseUtils responseUtils;

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @InjectMocks
    private UserProfileProcessor processor;

    private RequestContext context;
    private Resource resource;
    private HttpHeaders headers;

    @BeforeEach
    void setUp() {
        // Set up headers
        headers = new HttpHeaders();

        // Set up request
        when(exchange.getRequest()).thenReturn(request);
        when(request.getHeaders()).thenReturn(headers);

        // Set up resource
        resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/user/profile");
        resource.setEndpoint("http://user-service/api/user/profile");
        resource.setOperation(GET_LOGGED_IN_USER_PROFILE);

        // Set up context
        context = new RequestContext(exchange, Mono.empty());
        context.setResource(resource);
    }

    @Test
    @DisplayName("Should process user profile request and return response")
    void process_ValidRequest_ReturnsUserProfile() {
        // Arrange
        UserInfoResponse userInfo = new UserInfoResponse();
        UserProfile userProfile = new UserProfile();
        userProfile.setUsername("testuser");
        userProfile.setEmail("<EMAIL>");
        userInfo.setBody(userProfile);
        userInfo.setHeader(new WsHeader());

        when(identityService.getUserInfo(any(HttpHeaders.class))).thenReturn(Mono.just(userInfo));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertFalse(processingResult.isContinueProcessing());
                assertNotNull(processingResult.getResponse());
                assertEquals(HttpStatus.OK, processingResult.getResponse().getStatusCode());
            })
            .verifyComplete();

        verify(identityService).getUserInfo(any(HttpHeaders.class));
    }

    @Test
    @DisplayName("Should handle error when retrieving user profile")
    void process_ErrorRetrievingUserProfile_ReturnsErrorResponse() {
        // Arrange
        when(identityService.getUserInfo(any(HttpHeaders.class))).thenReturn(Mono.error(new RuntimeException("Error retrieving user profile")));

        ResponseEntity<byte[]> errorResponse = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        when(responseUtils.errorResponseReactive(
                any(HttpHeaders.class),
                eq(resource),
                eq(HttpStatus.INTERNAL_SERVER_ERROR),
                eq("GW_ERR9"),
                anyString()
        )).thenReturn(Mono.just(errorResponse));

        // Act
        Mono<ProcessingResult> result = processor.process(context);

        // Assert
        StepVerifier.create(result)
            .assertNext(processingResult -> {
                assertFalse(processingResult.isContinueProcessing());
                assertEquals(errorResponse, processingResult.getResponse());
            })
            .verifyComplete();

        verify(identityService).getUserInfo(any(HttpHeaders.class));
        verify(responseUtils).errorResponseReactive(
                any(HttpHeaders.class),
                eq(resource),
                eq(HttpStatus.INTERNAL_SERVER_ERROR),
                eq("GW_ERR9"),
                anyString()
        );
    }

    @Test
    @DisplayName("Should process only user profile requests")
    void shouldProcessIfEnabled_UserProfileRequest_ReturnsTrue() {
        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertTrue(shouldProcess);
    }

    @Test
    @DisplayName("Should not process non-user profile requests")
    void shouldProcessIfEnabled_NonUserProfileRequest_ReturnsFalse() {
        // Arrange
        resource.setOperation("SOME_OTHER_OPERATION");

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertFalse(shouldProcess);
    }

    @Test
    @DisplayName("Should not process when resource is null")
    void shouldProcessIfEnabled_NullResource_ReturnsFalse() {
        // Arrange
        context.setResource(null);

        // Act
        boolean shouldProcess = processor.shouldProcessIfEnabled(context);

        // Assert
        assertFalse(shouldProcess);
    }

    @Test
    @DisplayName("Should return correct default order")
    void getDefaultOrder_ReturnsCorrectValue() {
        assertEquals(30, processor.getDefaultOrder());
    }
}
