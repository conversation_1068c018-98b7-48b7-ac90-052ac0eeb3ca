package com.safaricom.dxl.partner.proxy.processor;

import com.safaricom.dxl.partner.proxy.config.MsConfigProperties;
import com.safaricom.dxl.partner.proxy.model.ProcessingResult;
import com.safaricom.dxl.partner.proxy.model.RequestContext;
import com.safaricom.dxl.partner.proxy.model.Resource;
import com.safaricom.dxl.partner.proxy.utils.ResponseUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.ES;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_MSISDN;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class HeaderValidationProcessorTest {

    @Mock
    private MsConfigProperties configProperties;

    @Mock
    private ResponseUtils responseUtils;

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @InjectMocks
    private HeaderValidationProcessor processor;

    private RequestContext context;
    private Resource resource;
    private HttpHeaders headers;

    @BeforeEach
    void setUp() {
        // Set up request
        headers = new HttpHeaders();
        when(exchange.getRequest()).thenReturn(request);
        when(request.getMethod()).thenReturn(HttpMethod.GET);
        when(request.getHeaders()).thenReturn(headers);

        // Set up resource
        resource = new Resource();
        resource.setMethod("GET");
        resource.setPath("/users");
        resource.setEndpoint("http://user-service/api/users");
        resource.setOperation("GET_USERS");
        resource.setExemptMsisdnHeader(false);

        // Set up context
        context = new RequestContext(exchange, Mono.empty());
        context.setResource(resource);

        // Set up config properties
        when(configProperties.getMsisdnRegex()).thenReturn("^[0-9]{12}$");
    }

    @Nested
    @DisplayName("process method tests")
    class ProcessTests {

        @Test
        @DisplayName("Should continue processing when all required headers are present")
        void process_AllRequiredHeadersPresent_ContinuesProcessing() {
            // Arrange
            headers.add(X_MSISDN, "254712345678");

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
                .verifyComplete();
        }

        @Test
        @DisplayName("Should continue processing when MSISDN header is exempt")
        void process_MsisdnHeaderExempt_ContinuesProcessing() {
            // Arrange
            resource.setExemptMsisdnHeader(true);

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
                .verifyComplete();
        }

        @Test
        @DisplayName("Should return 400 when required MSISDN header is missing")
        void process_MsisdnHeaderMissing_Returns400() {
            // Arrange
            ResponseEntity<byte[]> errorResponse = ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            when(responseUtils.errorResponseReactive(
                    any(HttpHeaders.class),
                    eq(resource),
                    eq(HttpStatus.BAD_REQUEST),
                    eq("IV_ERR1"),
                    eq(ES)
            )).thenReturn(Mono.just(errorResponse));

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> {
                    assertFalse(processingResult.isContinueProcessing());
                    assertEquals(errorResponse, processingResult.getResponse());
                })
                .verifyComplete();

            verify(responseUtils).errorResponseReactive(
                    any(HttpHeaders.class),
                    eq(resource),
                    eq(HttpStatus.BAD_REQUEST),
                    eq("IV_ERR1"),
                    eq(ES)
            );
        }

        @ParameterizedTest
        @ValueSource(strings = {"invalid-msisdn", "   ", ""})
        @DisplayName("Should return 400 when MSISDN header has invalid format, is blank, or empty")
        void process_InvalidMsisdnHeader_Returns400(String invalidMsisdn) {
            // Arrange
            headers.add(X_MSISDN, invalidMsisdn);

            ResponseEntity<byte[]> errorResponse = ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            when(responseUtils.errorResponseReactive(
                    any(HttpHeaders.class),
                    eq(resource),
                    eq(HttpStatus.BAD_REQUEST),
                    eq("IV_ERR2"),
                    eq(ES)
            )).thenReturn(Mono.just(errorResponse));

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> {
                    assertFalse(processingResult.isContinueProcessing());
                    assertEquals(errorResponse, processingResult.getResponse());
                })
                .verifyComplete();

            verify(responseUtils).errorResponseReactive(
                    any(HttpHeaders.class),
                    eq(resource),
                    eq(HttpStatus.BAD_REQUEST),
                    eq("IV_ERR2"),
                    eq(ES)
            );
        }

        @Test
        @DisplayName("Should remove invalid MSISDN header when it's exempt")
        void process_InvalidMsisdnWhenExempt_RemovesHeader() {
            // Arrange
            resource.setExemptMsisdnHeader(true);
            headers.add(X_MSISDN, "invalid-msisdn");
            when(configProperties.getMsisdnRegex()).thenReturn("^[0-9]{12}$");

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> {
                    assertTrue(processingResult.isContinueProcessing());
                    assertNull(headers.getFirst(X_MSISDN));
                })
                .verifyComplete();
        }



        @Test
        @DisplayName("Should continue processing when MSISDN is valid and exempt")
        void process_ValidMsisdnWhenExempt_ContinuesProcessing() {
            // Arrange
            resource.setExemptMsisdnHeader(true);
            headers.add(X_MSISDN, "254712345678"); // Valid MSISDN

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> {
                    assertTrue(processingResult.isContinueProcessing());
                    assertEquals("254712345678", headers.getFirst(X_MSISDN)); // Should not be removed
                })
                .verifyComplete();
        }

        @Test
        @DisplayName("Should continue processing when exemptMsisdnHeader is null (treated as exempt)")
        void process_NullExemptMsisdnHeader_TreatsAsExempt() {
            // Arrange
            resource.setExemptMsisdnHeader(null); // null is treated as exempt (not Boolean.FALSE)
            headers.add(X_MSISDN, "254712345678"); // Valid MSISDN

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
                .verifyComplete();
        }

        @Test
        @DisplayName("Should continue processing when exemptMsisdnHeader is null and MSISDN is missing")
        void process_NullExemptMsisdnHeaderWithMissingMsisdn_ContinuesProcessing() {
            // Arrange
            resource.setExemptMsisdnHeader(null); // null is treated as exempt (not Boolean.FALSE)
            // No MSISDN header added

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> assertTrue(processingResult.isContinueProcessing()))
                .verifyComplete();
        }

        @Test
        @DisplayName("Should remove invalid MSISDN when exemptMsisdnHeader is null")
        void process_NullExemptMsisdnHeaderWithInvalidMsisdn_RemovesHeader() {
            // Arrange
            resource.setExemptMsisdnHeader(null); // null is treated as exempt
            headers.add(X_MSISDN, "invalid-msisdn");

            // Act
            Mono<ProcessingResult> result = processor.process(context);

            // Assert
            StepVerifier.create(result)
                .assertNext(processingResult -> {
                    assertTrue(processingResult.isContinueProcessing());
                    assertNull(headers.getFirst(X_MSISDN)); // Should be removed
                })
                .verifyComplete();
        }

    }

    @Test
    @DisplayName("Should return correct default order")
    void getDefaultOrder_ReturnsCorrectValue() {
        assertEquals(20, processor.getDefaultOrder());
    }
}
