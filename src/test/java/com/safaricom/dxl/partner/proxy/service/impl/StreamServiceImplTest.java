package com.safaricom.dxl.partner.proxy.service.impl;

import com.safaricom.dxl.partner.proxy.model.Activity;
import com.safaricom.dxl.webflux.starter.service.WsStarterStreamProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class StreamServiceImplTest {

    @Mock
    private WsStarterStreamProducer streamProducer;

    @InjectMocks
    private StreamServiceImpl streamService;

    private Activity activity;
    private final String testTopic = "test-topic";

    @BeforeEach
    void setUp() {
        activity = new Activity(
            "test-conversation-id",
            "test-user",
            "<EMAIL>",
            "254712345678",
            "TEST_OPERATION",
            LocalDateTime.now()
        );

        // Set the topic field using reflection
        ReflectionTestUtils.setField(streamService, "topic", testTopic);
    }

    // Create a test subclass that overrides the serializeJson method
    static class TestStreamServiceImpl extends StreamServiceImpl {
        private final boolean returnNull;

        public TestStreamServiceImpl(WsStarterStreamProducer streamProducer, boolean returnNull) {
            super(streamProducer);
            this.returnNull = returnNull;
        }

        @Override
        protected String serializeActivity(Activity activity) {
            return returnNull ? null : super.serializeActivity(activity);
        }
    }

    @Test
    @DisplayName("Should handle serialization failure")
    void publish_SerializationFailure_LogsError() {
        // Arrange - Create a test service that returns null for serialization
        TestStreamServiceImpl testService = new TestStreamServiceImpl(streamProducer, true);
        ReflectionTestUtils.setField(testService, "topic", testTopic);

        Activity badActivity = new Activity(
            "test-conversation-id",
            "test-user",
            "<EMAIL>",
            "254712345678",
            "TEST_OPERATION",
            null
        );

        // Act
        Mono<Void> result = testService.publish(badActivity);

        // Assert
        StepVerifier.create(result)
            .verifyComplete();

        // Verify that the stream producer was not called
        verify(streamProducer, never()).produce(anyString(), anyString());
    }

    @Test
    @DisplayName("Should publish activity to Kafka")
    void publish_ValidActivity_PublishesToKafka() {
        // Act
        Mono<Void> result = streamService.publish(activity);

        // Assert
        StepVerifier.create(result)
                .verifyComplete();

        // Verify that the stream producer was called with the correct arguments
        verify(streamProducer).produce(eq(testTopic), anyString());
    }

    @Test
    @DisplayName("Should handle null activity")
    void publish_NullActivity_HandlesGracefully() {
        // Act
        Mono<Void> result = streamService.publish(null);

        // Assert
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof NullPointerException)
                .verify();
    }

    @Test
    @DisplayName("Should handle producer exception")
    void publish_ProducerException_HandlesGracefully() {
        // Arrange - Set up the streamProducer to throw an exception
        org.mockito.Mockito.doThrow(new RuntimeException("Test exception"))
            .when(streamProducer).produce(anyString(), anyString());

        // Act
        Mono<Void> result = streamService.publish(activity);

        // Assert
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                                  throwable.getMessage().equals("Test exception"))
                .verify();
    }


}
