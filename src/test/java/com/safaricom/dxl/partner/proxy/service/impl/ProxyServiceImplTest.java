package com.safaricom.dxl.partner.proxy.service.impl;

import com.safaricom.dxl.partner.proxy.service.RequestPipeline;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProxyServiceImplTest {

    @Mock
    private RequestPipeline requestPipeline;

    @Mock
    private ServerWebExchange exchange;

    @Mock
    private ServerHttpRequest request;

    @InjectMocks
    private ProxyServiceImpl proxyService;

    private Mono<byte[]> body;
    private ResponseEntity<byte[]> expectedResponse;

    @BeforeEach
    void setUp() {
        // Set up body - only what's actually needed for the tests
        body = Mono.just(new byte[0]);

        // Set up expected response
        expectedResponse = ResponseEntity.ok().build();
    }

    @Test
    @DisplayName("Should delegate request processing to the pipeline")
    void processRequest_ValidRequest_DelegatesToPipeline() {
        // Arrange
        when(requestPipeline.process(exchange, body)).thenReturn(Mono.just(expectedResponse));

        // Act
        Mono<ResponseEntity<byte[]>> result = proxyService.processRequest(exchange, body);

        // Assert
        StepVerifier.create(result)
            .expectNext(expectedResponse)
            .verifyComplete();

        verify(requestPipeline).process(exchange, body);
    }

    @Test
    @DisplayName("Should propagate errors from the pipeline")
    void processRequest_PipelineError_PropagatesError() {
        // Arrange
        RuntimeException exception = new RuntimeException("Test error");
        when(requestPipeline.process(exchange, body)).thenReturn(Mono.error(exception));

        // Act
        Mono<ResponseEntity<byte[]>> result = proxyService.processRequest(exchange, body);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(error -> error.equals(exception))
            .verify();

        verify(requestPipeline).process(exchange, body);
    }
}
