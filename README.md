# MS Partner Proxy

A reactive API gateway service that routes requests to appropriate backend services based on dynamic configuration.

## Overview

MS Partner Proxy is a flexible and extensible proxy service built with Spring WebFlux that sits between frontend applications and backend microservices. It provides a unified entry point for client applications while abstracting away the complexity of the underlying service architecture.

The service dynamically loads resource configurations at startup, which define the routing rules, authentication requirements, and other processing details for each endpoint. This allows for centralized management of API endpoints without requiring code changes.

## Key Features

- **Dynamic Routing**: Routes requests to appropriate backend services based on configuration
- **Request Processing Pipeline**: Extensible chain of processors for handling various aspects of request processing
- **Authentication & Authorization**: Supports multiple authentication methods and permission-based authorization
- **Header Validation**: Validates required headers based on resource configuration
- **Timeout Handling**: Configurable request timeouts with appropriate error responses
- **User Parameter Caching**: Caches user parameters for improved performance
- **Reactive Architecture**: Built with Spring WebFlux for non-blocking, reactive request processing
- **Centralized Configuration**: Loads resource configurations from a config server

## Architecture

### Components

The service is built around a request processing pipeline that consists of the following components:

1. **ProxyController**: Entry point for all requests
2. **ProxyService**: Delegates requests to the processing pipeline
3. **RequestPipeline**: Orchestrates the execution of processors in order
4. **RequestProcessors**: Individual components that handle specific aspects of request processing
5. **ResourceRegistry**: Stores and provides access to validated resource configurations
6. **ResourceConfigsLoader**: Loads resource configurations from the config server

### Request Flow

1. A request is received by the `ProxyController`
2. The controller delegates to the `ProxyService`
3. The service creates a `RequestContext` and passes it to the `RequestPipeline`
4. The pipeline executes each processor in order
5. Each processor can either:
   - Continue processing, allowing the next processor to execute
   - Terminate processing with a response, which is returned to the client
6. If all processors complete successfully, the request is forwarded to the target service
7. The response from the target service is returned to the client


### Processors

The following processors are included (in typical order):

1. **ResourceResolutionProcessor**: Resolves the appropriate resource configuration for the request
2. **HeaderValidationProcessor**: Validates required headers based on resource configuration
3. **UserProfileProcessor**: Handles requests for user profile information
4. **AuthenticationProcessor**: Authenticates the user by retrieving their profile
5. **AuthorizationProcessor**: Authorizes the user based on their permissions
6. **UserLoginProcessor**: Caches user profile data (email and phone) on user login operations for fast access.
7. **UserLogoutProcessor**: Deletes user profile data from the cache on user logout operations.
8. **UserProfileCachingProcessor**: Caches user profile data (email and phone) as a JSON object for faster access during other operations.
9. **TimeoutProcessor**: Adds configurable timeout handling to requests
10. **RequestForwardingProcessor**: Forwards the request to the target service
11. **LoggingProcessor**: Logs request details and adds a conversation ID if missing

**Note:** `UserLoginProcessor` and `UserLogoutProcessor` are placed immediately after `AuthorizationProcessor` and before `UserProfileCachingProcessor` to ensure user profile cache is managed correctly during login and logout flows.

## Configuration

### Resource Configuration

Resources are configured in YAML format and loaded from a config server. Each resource defines:

```yaml
document:
  - group: api
    resources:
      - method: GET
        path: /users
        endpoint: http://user-service/api/users
        operation: GET_USERS
        microservice: user-service
        authType: basic
        basicAuthCredentials: development
        exemptMsisdnHeader: false
        logActivity: true
        permission: READ_USERS
```

### Processor Configuration

Processors can be configured through application properties:

```properties
# Enable/disable specific processors
dxl.ms.processors.enabled.ResourceResolutionProcessor=true
dxl.ms.processors.enabled.LoggingProcessor=true
dxl.ms.processors.enabled.HeaderValidationProcessor=true
dxl.ms.processors.enabled.UserProfileProcessor=true
dxl.ms.processors.enabled.UserLoginProcessor=true
dxl.ms.processors.enabled.UserLogoutProcessor=true
dxl.ms.processors.enabled.AuthenticationProcessor=true
dxl.ms.processors.enabled.AuthorizationProcessor=true
dxl.ms.processors.enabled.UserProfileCachingProcessor=true
dxl.ms.processors.enabled.TimeoutProcessor=true
dxl.ms.processors.enabled.RequestForwardingProcessor=true

# Configure processor order (lower values execute earlier)
dxl.ms.processors.order.ResourceResolutionProcessor=1
dxl.ms.processors.order.LoggingProcessor=10
dxl.ms.processors.order.HeaderValidationProcessor=20
dxl.ms.processors.order.UserProfileProcessor=30
dxl.ms.processors.order.UserLoginProcessor=40
dxl.ms.processors.order.UserLogoutProcessor=41
dxl.ms.processors.order.AuthenticationProcessor=50
dxl.ms.processors.order.AuthorizationProcessor=60
dxl.ms.processors.order.UserProfileCachingProcessor=70
dxl.ms.processors.order.TimeoutProcessor=90
dxl.ms.processors.order.RequestForwardingProcessor=100

# Processor-specific settings
dxl.ms.processors.settings.HeaderValidationProcessor.strictValidation=true
dxl.ms.processors.settings.TimeoutProcessor.timeoutSeconds=30

# Processor-specific configurations
dxl.ms.processors.configs.UserParamCachingProcessor.profiles.email=email
dxl.ms.processors.configs.UserParamCachingProcessor.profiles.phoneNumber=phone
```

## Getting Started

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher
- Access to a Spring Cloud Config Server (for resource configurations)

### Building the Application

```bash
mvn clean package
```

### Running the Application

```bash
java -jar target/ms-partner-proxy-1.0.0.jar
```

### Configuration

The application requires the following configuration properties:

```properties
server.port=8080
spring.service.name=ms-partner-proxy
spring.profiles.active=development
spring.config.import=optional:configserver:http://config-server-url:8888
spring.application.name=dxl-ms-starter,ms-partner-proxy
```

## Extending the Application

### Creating Custom Processors

To create a custom processor:

1. Create a class that extends `AbstractRequestProcessor`
2. Annotate it with `@Component` and `@RequiredArgsConstructor` for dependency injection
3. Override the `getDefaultOrder` method to specify its position in the pipeline
4. Add a `@PostConstruct` init method for initialization logging
5. Override the `process` method to implement the processing logic
6. Optionally override `shouldProcessIfEnabled` to implement conditional processing

Example:

```java
@Component
@RequiredArgsConstructor
public class CustomProcessor extends AbstractRequestProcessor {

    private final SomeDependency dependency;

    @Override
    protected int getDefaultOrder() {
        return 15; // Specify the default order
    }

    @PostConstruct
    public void init() {
        LoggingUtils.info("CustomProcessor initialized");
    }

    @Override
    public Mono<ProcessingResult> process(RequestContext context) {
        // Process the request
        return Mono.just(ProcessingResult.continueProcessing());
    }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/my-feature`)
3. Commit your changes (`git commit -m 'Add my feature'`)
4. Push to the branch (`git push origin feature/my-feature`)
5. Create a new Pull Request

## License

This project is proprietary and confidential. Unauthorized copying, transfer, or use is strictly prohibited.

## Authors

- **Nicholas Tanui** - *Initial work*